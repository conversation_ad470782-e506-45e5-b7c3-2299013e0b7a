{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\dashboard\\\\Dashboard.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport WeatherData from \"../pages/weatherAndTime/WeatherData\";\nimport { API_URL } from \"../common/fetchData/apiConfig\";\nimport { useNavigate } from \"react-router-dom\";\nimport Loading from \"../common/Loading\";\nimport { getWorldTimeStrings } from \"../utils/worldTimeUtils\";\nimport WelcomeCard from \"./WelcomeCard\";\nimport ClientTeamsSection from \"./ClientTeamsSection\";\nimport ShiftSummarySection from \"./ShiftSummarySection\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst isTokenValid = () => {\n  const token = localStorage.getItem(\"token\");\n  return token !== null && token !== \"\";\n};\nconst Dashboard = () => {\n  _s();\n  const [userData, setUserData] = React.useState(null);\n  const [error, setError] = React.useState(null);\n  const [filterOptionLoading, setFilterOptionLoading] = React.useState(false);\n  const navigate = useNavigate();\n\n  // Real-time date/time strings\n  const [dateTimeStrings, setDateTimeStrings] = React.useState(null);\n  React.useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    if (!isTokenValid()) {\n      setError(\"No valid authentication token found.\");\n      setFilterOptionLoading(false);\n      navigate(\"/login\");\n      return;\n    }\n    const user = localStorage.getItem(\"user\");\n    if (user) {\n      setUserData(JSON.parse(user));\n      return;\n    }\n    const fetchUserData = async () => {\n      setFilterOptionLoading(true);\n      try {\n        const response = await fetch(`${API_URL}/logged-users`, {\n          method: \"GET\",\n          headers: {\n            Authorization: `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n          }\n        });\n        if (!response.ok) throw new Error(\"Failed to fetch user data\");\n        const data = await response.json();\n        setUserData(data);\n      } catch (err) {\n        setError(err.message);\n      } finally {\n        setFilterOptionLoading(false);\n      }\n    };\n    fetchUserData();\n  }, [navigate]);\n  if (filterOptionLoading) return /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 35\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"rounded-xl\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-12 gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-span-12 lg:col-span-7\",\n        children: /*#__PURE__*/_jsxDEV(WelcomeCard, {\n          userData: userData,\n          dateTimeStrings: dateTimeStrings\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-span-12 lg:col-span-5 flex flex-wrap border border-gray-300 dark:border-gray-600 rounded-2xl\",\n        children: /*#__PURE__*/_jsxDEV(WeatherData, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4\",\n      children: /*#__PURE__*/_jsxDEV(ClientTeamsSection, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4\",\n      children: /*#__PURE__*/_jsxDEV(ShiftSummarySection, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 text-red-500\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"5Yqod7BWHSZPuP5JXVyvnZIYMpw=\", false, function () {\n  return [useNavigate];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "WeatherData", "API_URL", "useNavigate", "Loading", "getWorldTimeStrings", "WelcomeCard", "ClientTeamsSection", "ShiftSummarySection", "jsxDEV", "_jsxDEV", "isTokenValid", "token", "localStorage", "getItem", "Dashboard", "_s", "userData", "setUserData", "useState", "error", "setError", "filterOptionLoading", "setFilterOptionLoading", "navigate", "dateTimeStrings", "setDateTimeStrings", "useEffect", "user", "JSON", "parse", "fetchUserData", "response", "fetch", "method", "headers", "Authorization", "ok", "Error", "data", "json", "err", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/dashboard/Dashboard.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport WeatherData from \"../pages/weatherAndTime/WeatherData\";\r\nimport { API_URL } from \"../common/fetchData/apiConfig\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport Loading from \"../common/Loading\";\r\nimport { getWorldTimeStrings } from \"../utils/worldTimeUtils\";\r\n\r\nimport WelcomeCard from \"./WelcomeCard\";\r\nimport ClientTeamsSection from \"./ClientTeamsSection\";\r\nimport ShiftSummarySection from \"./ShiftSummarySection\";\r\n\r\nconst isTokenValid = () => {\r\n  const token = localStorage.getItem(\"token\");\r\n  return token !== null && token !== \"\";\r\n};\r\n\r\nconst Dashboard = () => {\r\n  const [userData, setUserData] = React.useState(null);\r\n  const [error, setError] = React.useState(null);\r\n  const [filterOptionLoading, setFilterOptionLoading] = React.useState(false);\r\n  const navigate = useNavigate();\r\n\r\n  // Real-time date/time strings\r\n  const [dateTimeStrings, setDateTimeStrings] = React.useState(null);\r\n\r\n  React.useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n\r\n    if (!isTokenValid()) {\r\n      setError(\"No valid authentication token found.\");\r\n      setFilterOptionLoading(false);\r\n      navigate(\"/login\");\r\n      return;\r\n    }\r\n\r\n    const user = localStorage.getItem(\"user\");\r\n    if (user) {\r\n      setUserData(JSON.parse(user));\r\n      return;\r\n    }\r\n\r\n    const fetchUserData = async () => {\r\n      setFilterOptionLoading(true);\r\n      try {\r\n        const response = await fetch(`${API_URL}/logged-users`, {\r\n          method: \"GET\",\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n        });\r\n        if (!response.ok) throw new Error(\"Failed to fetch user data\");\r\n        const data = await response.json();\r\n        setUserData(data);\r\n      } catch (err) {\r\n        setError(err.message);\r\n      } finally {\r\n        setFilterOptionLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchUserData();\r\n  }, [navigate]);\r\n\r\n  if (filterOptionLoading) return <Loading />;\r\n\r\n  return (\r\n    <div className=\"rounded-xl\">\r\n      {/* Top row: Welcome + Weather */}\r\n      <div className=\"grid grid-cols-12 gap-4\">\r\n        <div className=\"col-span-12 lg:col-span-7\">\r\n          <WelcomeCard userData={userData} dateTimeStrings={dateTimeStrings} />\r\n        </div>\r\n        <div className=\"col-span-12 lg:col-span-5 flex flex-wrap border border-gray-300 dark:border-gray-600 rounded-2xl\">\r\n          <WeatherData />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Clients/Teams grid */}\r\n      <div className=\"mt-4\">\r\n        <ClientTeamsSection />\r\n      </div>\r\n\r\n      {/* Shifts summary */}\r\n      <div className=\"mt-4\">\r\n        <ShiftSummarySection />\r\n      </div>\r\n\r\n      {error && <div className=\"mt-4 text-red-500\">{error}</div>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Dashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,OAAO,MAAM,mBAAmB;AACvC,SAASC,mBAAmB,QAAQ,yBAAyB;AAE7D,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,mBAAmB,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,OAAOF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE;AACvC,CAAC;AAED,MAAMG,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,KAAK,CAACmB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvB,KAAK,CAACmB,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAMK,QAAQ,GAAGrB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,KAAK,CAACmB,QAAQ,CAAC,IAAI,CAAC;EAElEnB,KAAK,CAAC2B,SAAS,CAAC,MAAM;IACpB,MAAMf,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI,CAACH,YAAY,CAAC,CAAC,EAAE;MACnBU,QAAQ,CAAC,sCAAsC,CAAC;MAChDE,sBAAsB,CAAC,KAAK,CAAC;MAC7BC,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEA,MAAMI,IAAI,GAAGf,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IACzC,IAAIc,IAAI,EAAE;MACRV,WAAW,CAACW,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC,CAAC;MAC7B;IACF;IAEA,MAAMG,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChCR,sBAAsB,CAAC,IAAI,CAAC;MAC5B,IAAI;QACF,MAAMS,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG/B,OAAO,eAAe,EAAE;UACtDgC,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACPC,aAAa,EAAE,UAAUxB,KAAK,EAAE;YAChC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QACF,IAAI,CAACoB,QAAQ,CAACK,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;QAC9D,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAClCtB,WAAW,CAACqB,IAAI,CAAC;MACnB,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZpB,QAAQ,CAACoB,GAAG,CAACC,OAAO,CAAC;MACvB,CAAC,SAAS;QACRnB,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF,CAAC;IAEDQ,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEd,IAAIF,mBAAmB,EAAE,oBAAOZ,OAAA,CAACN,OAAO;IAAAuC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAE3C,oBACEpC,OAAA;IAAKqC,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAEzBtC,OAAA;MAAKqC,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCtC,OAAA;QAAKqC,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eACxCtC,OAAA,CAACJ,WAAW;UAACW,QAAQ,EAAEA,QAAS;UAACQ,eAAe,EAAEA;QAAgB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACNpC,OAAA;QAAKqC,SAAS,EAAC,kGAAkG;QAAAC,QAAA,eAC/GtC,OAAA,CAACT,WAAW;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpC,OAAA;MAAKqC,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBtC,OAAA,CAACH,kBAAkB;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAGNpC,OAAA;MAAKqC,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBtC,OAAA,CAACF,mBAAmB;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,EAEL1B,KAAK,iBAAIV,OAAA;MAAKqC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAE5B;IAAK;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvD,CAAC;AAEV,CAAC;AAAC9B,EAAA,CA3EID,SAAS;EAAA,QAIIZ,WAAW;AAAA;AAAA8C,EAAA,GAJxBlC,SAAS;AA6Ef,eAAeA,SAAS;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}