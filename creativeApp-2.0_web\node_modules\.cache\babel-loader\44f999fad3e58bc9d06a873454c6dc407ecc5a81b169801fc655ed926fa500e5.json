{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\dashboard\\\\ClientTeamsSection.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport Loading from \"../common/Loading\";\nimport { API_URL } from \"../common/fetchData/apiConfig\";\n\n// Component to display a single team card\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TeamCard = ({\n  team\n}) => {\n  var _team$name, _team$name$;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 truncate\",\n          children: team.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [team.logo ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: team.logo.startsWith('http') ? team.logo : `${API_URL}${team.logo}`,\n          alt: `${team.name} logo`,\n          className: \"w-12 h-12 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-600\",\n          onError: e => {\n            e.target.style.display = 'none';\n            e.target.nextSibling.style.display = 'flex';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-lg font-medium text-gray-500\",\n          style: {\n            display: team.logo ? 'none' : 'flex'\n          },\n          children: ((_team$name = team.name) === null || _team$name === void 0 ? void 0 : (_team$name$ = _team$name[0]) === null || _team$name$ === void 0 ? void 0 : _team$name$.toUpperCase()) || \"T\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 space-y-2\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined text-gray-400\",\n          children: \"person\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Team Lead:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-gray-900 dark:text-gray-100\",\n          children: team.teamLead\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 grid grid-cols-2 gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600 dark:text-gray-300\",\n          children: \"Total Members\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDC65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold\",\n            children: String(team.totalMembers).padStart(2, '0')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600 dark:text-gray-300\",\n          children: \"Billable Hours\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u23F1\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold\",\n            children: [team.billableHours, \"hr\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 3\n  }, this);\n};\n\n// Infinite Slider Component\n_c = TeamCard;\nconst InfiniteSlider = ({\n  teams\n}) => {\n  _s();\n  const sliderRef = useRef(null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [startX, setStartX] = useState(0);\n  const [scrollLeft, setScrollLeft] = useState(0);\n  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);\n\n  // Create infinite loop by duplicating teams (more copies for smoother loop)\n  const infiniteTeams = [...teams, ...teams, ...teams, ...teams];\n\n  // Auto-scroll functionality\n  useEffect(() => {\n    if (!autoScrollEnabled || isDragging || teams.length === 0) return;\n    const interval = setInterval(() => {\n      if (sliderRef.current) {\n        const slider = sliderRef.current;\n        const cardWidth = 320; // Approximate card width + gap\n        const maxScroll = cardWidth * teams.length;\n\n        // Smooth infinite loop\n        if (slider.scrollLeft >= maxScroll) {\n          slider.scrollTo({\n            left: 0,\n            behavior: 'auto'\n          });\n        } else {\n          slider.scrollLeft += 0.5; // Slower, smoother scroll\n        }\n      }\n    }, 20); // Faster interval for smoother animation\n\n    return () => clearInterval(interval);\n  }, [autoScrollEnabled, isDragging, teams.length]);\n\n  // Mouse drag handlers\n  const handleMouseDown = e => {\n    setIsDragging(true);\n    setAutoScrollEnabled(false);\n    setStartX(e.pageX - sliderRef.current.offsetLeft);\n    setScrollLeft(sliderRef.current.scrollLeft);\n  };\n  const handleMouseMove = e => {\n    if (!isDragging) return;\n    e.preventDefault();\n    const x = e.pageX - sliderRef.current.offsetLeft;\n    const walk = (x - startX) * 2;\n    sliderRef.current.scrollLeft = scrollLeft - walk;\n  };\n  const handleMouseUp = () => {\n    setIsDragging(false);\n    setTimeout(() => setAutoScrollEnabled(true), 2000); // Resume auto-scroll after 2s\n  };\n\n  // Touch handlers for mobile\n  const handleTouchStart = e => {\n    setIsDragging(true);\n    setAutoScrollEnabled(false);\n    setStartX(e.touches[0].pageX - sliderRef.current.offsetLeft);\n    setScrollLeft(sliderRef.current.scrollLeft);\n  };\n  const handleTouchMove = e => {\n    if (!isDragging) return;\n    const x = e.touches[0].pageX - sliderRef.current.offsetLeft;\n    const walk = (x - startX) * 2;\n    sliderRef.current.scrollLeft = scrollLeft - walk;\n  };\n  const handleTouchEnd = () => {\n    setIsDragging(false);\n    setTimeout(() => setAutoScrollEnabled(true), 2000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: sliderRef,\n      className: \"flex gap-4 overflow-x-auto scrollbar-hide cursor-grab active:cursor-grabbing\",\n      style: {\n        scrollbarWidth: 'none',\n        msOverflowStyle: 'none',\n        WebkitOverflowScrolling: 'touch'\n      },\n      onMouseDown: handleMouseDown,\n      onMouseMove: handleMouseMove,\n      onMouseUp: handleMouseUp,\n      onMouseLeave: handleMouseUp,\n      onTouchStart: handleTouchStart,\n      onTouchMove: handleTouchMove,\n      onTouchEnd: handleTouchEnd,\n      children: infiniteTeams.map((team, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-shrink-0 w-80 sm:w-72 md:w-80\",\n        children: /*#__PURE__*/_jsxDEV(TeamCard, {\n          team: team\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this)\n      }, `${team.id}-${index}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-100 to-transparent pointer-events-none\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-100 to-transparent pointer-events-none\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n\n// Main component\n_s(InfiniteSlider, \"iCaoEGp6Dctr2xB2kfOrXVBkGeo=\");\n_c2 = InfiniteSlider;\nfunction ClientTeamsSection() {\n  _s2();\n  const {\n    teams,\n    loading,\n    error\n  } = useDashboardTeams();\n  if (loading) return /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-red-500 text-center py-4\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 171,\n    columnNumber: 21\n  }, this);\n  if (!teams.length) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-gray-500 text-center py-4\",\n    children: \"No teams available\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 29\n  }, this);\n  return /*#__PURE__*/_jsxDEV(InfiniteSlider, {\n    teams: teams\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 10\n  }, this);\n}\n\n// Add CSS for hiding scrollbars\n_s2(ClientTeamsSection, \"KQTK5qAH6FW4dvUZtN7lD6C4vR0=\", true);\n_c3 = ClientTeamsSection;\nconst style = document.createElement('style');\nstyle.textContent = `\n  .scrollbar-hide {\n    -ms-overflow-style: none;\n    scrollbar-width: none;\n  }\n  .scrollbar-hide::-webkit-scrollbar {\n    display: none;\n  }\n`;\nif (!document.head.querySelector('style[data-scrollbar-hide]')) {\n  style.setAttribute('data-scrollbar-hide', 'true');\n  document.head.appendChild(style);\n}\n\n// Export the component\nexport default ClientTeamsSection;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"TeamCard\");\n$RefreshReg$(_c2, \"InfiniteSlider\");\n$RefreshReg$(_c3, \"ClientTeamsSection\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Loading", "API_URL", "jsxDEV", "_jsxDEV", "TeamCard", "team", "_team$name", "_team$name$", "className", "children", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "logo", "src", "startsWith", "alt", "onError", "e", "target", "style", "display", "nextS<PERSON>ling", "toUpperCase", "teamLead", "String", "totalMembers", "padStart", "billableHours", "_c", "InfiniteSlider", "teams", "_s", "sliderRef", "isDragging", "setIsDragging", "startX", "setStartX", "scrollLeft", "setScrollLeft", "autoScrollEnabled", "setAutoScrollEnabled", "infiniteTeams", "length", "interval", "setInterval", "current", "slider", "<PERSON><PERSON><PERSON><PERSON>", "maxScroll", "scrollTo", "left", "behavior", "clearInterval", "handleMouseDown", "pageX", "offsetLeft", "handleMouseMove", "preventDefault", "x", "walk", "handleMouseUp", "setTimeout", "handleTouchStart", "touches", "handleTouchMove", "handleTouchEnd", "ref", "scrollbarWidth", "msOverflowStyle", "WebkitOverflowScrolling", "onMouseDown", "onMouseMove", "onMouseUp", "onMouseLeave", "onTouchStart", "onTouchMove", "onTouchEnd", "map", "index", "id", "_c2", "ClientTeamsSection", "_s2", "loading", "error", "useDashboardTeams", "_c3", "document", "createElement", "textContent", "head", "querySelector", "setAttribute", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/dashboard/ClientTeamsSection.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport Loading from \"../common/Loading\";\r\nimport { API_URL } from \"../common/fetchData/apiConfig\";\r\n\r\n// Component to display a single team card\r\nconst TeamCard = ({ team }) => (\r\n  <div className=\"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow p-4\">\r\n    <div className=\"flex items-center justify-between\">\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 truncate\">{team.name}</h3>\r\n      </div>\r\n      <div className=\"flex items-center gap-2\">\r\n        {team.logo ? (\r\n          <img\r\n            src={team.logo.startsWith('http') ? team.logo : `${API_URL}${team.logo}`}\r\n            alt={`${team.name} logo`}\r\n            className=\"w-12 h-12 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-600\"\r\n            onError={(e) => {\r\n              e.target.style.display = 'none';\r\n              e.target.nextSibling.style.display = 'flex';\r\n            }}\r\n          />\r\n        ) : null}\r\n        <div\r\n          className=\"w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-lg font-medium text-gray-500\"\r\n          style={{ display: team.logo ? 'none' : 'flex' }}\r\n        >\r\n          {team.name?.[0]?.toUpperCase() || \"T\"}\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div className=\"mt-4 space-y-2\">\r\n      <p className=\"text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2\">\r\n        <span className=\"material-symbols-outlined text-gray-400\">person</span>\r\n        <span>Team Lead:</span>\r\n        <span className=\"font-medium text-gray-900 dark:text-gray-100\">{team.teamLead}</span>\r\n      </p>\r\n    </div>\r\n\r\n    <div className=\"mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 grid grid-cols-2 gap-3\">\r\n      <div className=\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\">\r\n        <p className=\"text-xs text-gray-600 dark:text-gray-300\">Total Members</p>\r\n        <div className=\"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\">\r\n          <span>👥</span>\r\n          <span className=\"font-semibold\">{String(team.totalMembers).padStart(2, '0')}</span>\r\n        </div>\r\n      </div>\r\n      <div className=\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\">\r\n        <p className=\"text-xs text-gray-600 dark:text-gray-300\">Billable Hours</p>\r\n        <div className=\"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\">\r\n          <span>⏱️</span>\r\n          <span className=\"font-semibold\">{team.billableHours}hr</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\n// Infinite Slider Component\r\nconst InfiniteSlider = ({ teams }) => {\r\n  const sliderRef = useRef(null);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [startX, setStartX] = useState(0);\r\n  const [scrollLeft, setScrollLeft] = useState(0);\r\n  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);\r\n\r\n  // Create infinite loop by duplicating teams (more copies for smoother loop)\r\n  const infiniteTeams = [...teams, ...teams, ...teams, ...teams];\r\n\r\n  // Auto-scroll functionality\r\n  useEffect(() => {\r\n    if (!autoScrollEnabled || isDragging || teams.length === 0) return;\r\n\r\n    const interval = setInterval(() => {\r\n      if (sliderRef.current) {\r\n        const slider = sliderRef.current;\r\n        const cardWidth = 320; // Approximate card width + gap\r\n        const maxScroll = cardWidth * teams.length;\r\n\r\n        // Smooth infinite loop\r\n        if (slider.scrollLeft >= maxScroll) {\r\n          slider.scrollTo({ left: 0, behavior: 'auto' });\r\n        } else {\r\n          slider.scrollLeft += 0.5; // Slower, smoother scroll\r\n        }\r\n      }\r\n    }, 20); // Faster interval for smoother animation\r\n\r\n    return () => clearInterval(interval);\r\n  }, [autoScrollEnabled, isDragging, teams.length]);\r\n\r\n  // Mouse drag handlers\r\n  const handleMouseDown = (e) => {\r\n    setIsDragging(true);\r\n    setAutoScrollEnabled(false);\r\n    setStartX(e.pageX - sliderRef.current.offsetLeft);\r\n    setScrollLeft(sliderRef.current.scrollLeft);\r\n  };\r\n\r\n  const handleMouseMove = (e) => {\r\n    if (!isDragging) return;\r\n    e.preventDefault();\r\n    const x = e.pageX - sliderRef.current.offsetLeft;\r\n    const walk = (x - startX) * 2;\r\n    sliderRef.current.scrollLeft = scrollLeft - walk;\r\n  };\r\n\r\n  const handleMouseUp = () => {\r\n    setIsDragging(false);\r\n    setTimeout(() => setAutoScrollEnabled(true), 2000); // Resume auto-scroll after 2s\r\n  };\r\n\r\n  // Touch handlers for mobile\r\n  const handleTouchStart = (e) => {\r\n    setIsDragging(true);\r\n    setAutoScrollEnabled(false);\r\n    setStartX(e.touches[0].pageX - sliderRef.current.offsetLeft);\r\n    setScrollLeft(sliderRef.current.scrollLeft);\r\n  };\r\n\r\n  const handleTouchMove = (e) => {\r\n    if (!isDragging) return;\r\n    const x = e.touches[0].pageX - sliderRef.current.offsetLeft;\r\n    const walk = (x - startX) * 2;\r\n    sliderRef.current.scrollLeft = scrollLeft - walk;\r\n  };\r\n\r\n  const handleTouchEnd = () => {\r\n    setIsDragging(false);\r\n    setTimeout(() => setAutoScrollEnabled(true), 2000);\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative overflow-hidden\">\r\n      <div\r\n        ref={sliderRef}\r\n        className=\"flex gap-4 overflow-x-auto scrollbar-hide cursor-grab active:cursor-grabbing\"\r\n        style={{\r\n          scrollbarWidth: 'none',\r\n          msOverflowStyle: 'none',\r\n          WebkitOverflowScrolling: 'touch'\r\n        }}\r\n        onMouseDown={handleMouseDown}\r\n        onMouseMove={handleMouseMove}\r\n        onMouseUp={handleMouseUp}\r\n        onMouseLeave={handleMouseUp}\r\n        onTouchStart={handleTouchStart}\r\n        onTouchMove={handleTouchMove}\r\n        onTouchEnd={handleTouchEnd}\r\n      >\r\n        {infiniteTeams.map((team, index) => (\r\n          <div key={`${team.id}-${index}`} className=\"flex-shrink-0 w-80 sm:w-72 md:w-80\">\r\n            <TeamCard team={team} />\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Gradient overlays for smooth edges */}\r\n      <div className=\"absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-100 to-transparent pointer-events-none\" />\r\n      <div className=\"absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-100 to-transparent pointer-events-none\" />\r\n    </div>\r\n  );\r\n};\r\n\r\n// Main component\r\nfunction ClientTeamsSection() {\r\n  const { teams, loading, error } = useDashboardTeams();\r\n\r\n  if (loading) return <Loading />;\r\n  if (error) return <div className=\"text-red-500 text-center py-4\">{error}</div>;\r\n  if (!teams.length) return <div className=\"text-gray-500 text-center py-4\">No teams available</div>;\r\n\r\n  return <InfiniteSlider teams={teams} />;\r\n}\r\n\r\n// Add CSS for hiding scrollbars\r\nconst style = document.createElement('style');\r\nstyle.textContent = `\r\n  .scrollbar-hide {\r\n    -ms-overflow-style: none;\r\n    scrollbar-width: none;\r\n  }\r\n  .scrollbar-hide::-webkit-scrollbar {\r\n    display: none;\r\n  }\r\n`;\r\nif (!document.head.querySelector('style[data-scrollbar-hide]')) {\r\n  style.setAttribute('data-scrollbar-hide', 'true');\r\n  document.head.appendChild(style);\r\n}\r\n\r\n// Export the component\r\nexport default ClientTeamsSection;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,OAAO,MAAM,mBAAmB;AACvC,SAASC,OAAO,QAAQ,+BAA+B;;AAEvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC;EAAA,IAAAC,UAAA,EAAAC,WAAA;EAAA,oBACxBJ,OAAA;IAAKK,SAAS,EAAC,mIAAmI;IAAAC,QAAA,gBAChJN,OAAA;MAAKK,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDN,OAAA;QAAAM,QAAA,eACEN,OAAA;UAAIK,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAAEJ,IAAI,CAACK;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F,CAAC,eACNX,OAAA;QAAKK,SAAS,EAAC,yBAAyB;QAAAC,QAAA,GACrCJ,IAAI,CAACU,IAAI,gBACRZ,OAAA;UACEa,GAAG,EAAEX,IAAI,CAACU,IAAI,CAACE,UAAU,CAAC,MAAM,CAAC,GAAGZ,IAAI,CAACU,IAAI,GAAG,GAAGd,OAAO,GAAGI,IAAI,CAACU,IAAI,EAAG;UACzEG,GAAG,EAAE,GAAGb,IAAI,CAACK,IAAI,OAAQ;UACzBF,SAAS,EAAC,6EAA6E;UACvFW,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;YAC/BH,CAAC,CAACC,MAAM,CAACG,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,MAAM;UAC7C;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACA,IAAI,eACRX,OAAA;UACEK,SAAS,EAAC,wHAAwH;UAClIc,KAAK,EAAE;YAAEC,OAAO,EAAElB,IAAI,CAACU,IAAI,GAAG,MAAM,GAAG;UAAO,CAAE;UAAAN,QAAA,EAE/C,EAAAH,UAAA,GAAAD,IAAI,CAACK,IAAI,cAAAJ,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAgBkB,WAAW,CAAC,CAAC,KAAI;QAAG;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENX,OAAA;MAAKK,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BN,OAAA;QAAGK,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAC7EN,OAAA;UAAMK,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvEX,OAAA;UAAAM,QAAA,EAAM;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvBX,OAAA;UAAMK,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAEJ,IAAI,CAACqB;QAAQ;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENX,OAAA;MAAKK,SAAS,EAAC,gFAAgF;MAAAC,QAAA,gBAC7FN,OAAA;QAAKK,SAAS,EAAC,2FAA2F;QAAAC,QAAA,gBACxGN,OAAA;UAAGK,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzEX,OAAA;UAAKK,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAC5EN,OAAA;YAAAM,QAAA,EAAM;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfX,OAAA;YAAMK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEkB,MAAM,CAACtB,IAAI,CAACuB,YAAY,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG;UAAC;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNX,OAAA;QAAKK,SAAS,EAAC,2FAA2F;QAAAC,QAAA,gBACxGN,OAAA;UAAGK,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC1EX,OAAA;UAAKK,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAC5EN,OAAA;YAAAM,QAAA,EAAM;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfX,OAAA;YAAMK,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAEJ,IAAI,CAACyB,aAAa,EAAC,IAAE;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAAA,CACP;;AAED;AAAAiB,EAAA,GAtDM3B,QAAQ;AAuDd,MAAM4B,cAAc,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAMC,SAAS,GAAGpC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwC,MAAM,EAAEC,SAAS,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC4C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAM8C,aAAa,GAAG,CAAC,GAAGX,KAAK,EAAE,GAAGA,KAAK,EAAE,GAAGA,KAAK,EAAE,GAAGA,KAAK,CAAC;;EAE9D;EACApC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6C,iBAAiB,IAAIN,UAAU,IAAIH,KAAK,CAACY,MAAM,KAAK,CAAC,EAAE;IAE5D,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,IAAIZ,SAAS,CAACa,OAAO,EAAE;QACrB,MAAMC,MAAM,GAAGd,SAAS,CAACa,OAAO;QAChC,MAAME,SAAS,GAAG,GAAG,CAAC,CAAC;QACvB,MAAMC,SAAS,GAAGD,SAAS,GAAGjB,KAAK,CAACY,MAAM;;QAE1C;QACA,IAAII,MAAM,CAACT,UAAU,IAAIW,SAAS,EAAE;UAClCF,MAAM,CAACG,QAAQ,CAAC;YAAEC,IAAI,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAO,CAAC,CAAC;QAChD,CAAC,MAAM;UACLL,MAAM,CAACT,UAAU,IAAI,GAAG,CAAC,CAAC;QAC5B;MACF;IACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;IAER,OAAO,MAAMe,aAAa,CAACT,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACJ,iBAAiB,EAAEN,UAAU,EAAEH,KAAK,CAACY,MAAM,CAAC,CAAC;;EAEjD;EACA,MAAMW,eAAe,GAAIpC,CAAC,IAAK;IAC7BiB,aAAa,CAAC,IAAI,CAAC;IACnBM,oBAAoB,CAAC,KAAK,CAAC;IAC3BJ,SAAS,CAACnB,CAAC,CAACqC,KAAK,GAAGtB,SAAS,CAACa,OAAO,CAACU,UAAU,CAAC;IACjDjB,aAAa,CAACN,SAAS,CAACa,OAAO,CAACR,UAAU,CAAC;EAC7C,CAAC;EAED,MAAMmB,eAAe,GAAIvC,CAAC,IAAK;IAC7B,IAAI,CAACgB,UAAU,EAAE;IACjBhB,CAAC,CAACwC,cAAc,CAAC,CAAC;IAClB,MAAMC,CAAC,GAAGzC,CAAC,CAACqC,KAAK,GAAGtB,SAAS,CAACa,OAAO,CAACU,UAAU;IAChD,MAAMI,IAAI,GAAG,CAACD,CAAC,GAAGvB,MAAM,IAAI,CAAC;IAC7BH,SAAS,CAACa,OAAO,CAACR,UAAU,GAAGA,UAAU,GAAGsB,IAAI;EAClD,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B1B,aAAa,CAAC,KAAK,CAAC;IACpB2B,UAAU,CAAC,MAAMrB,oBAAoB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACtD,CAAC;;EAED;EACA,MAAMsB,gBAAgB,GAAI7C,CAAC,IAAK;IAC9BiB,aAAa,CAAC,IAAI,CAAC;IACnBM,oBAAoB,CAAC,KAAK,CAAC;IAC3BJ,SAAS,CAACnB,CAAC,CAAC8C,OAAO,CAAC,CAAC,CAAC,CAACT,KAAK,GAAGtB,SAAS,CAACa,OAAO,CAACU,UAAU,CAAC;IAC5DjB,aAAa,CAACN,SAAS,CAACa,OAAO,CAACR,UAAU,CAAC;EAC7C,CAAC;EAED,MAAM2B,eAAe,GAAI/C,CAAC,IAAK;IAC7B,IAAI,CAACgB,UAAU,EAAE;IACjB,MAAMyB,CAAC,GAAGzC,CAAC,CAAC8C,OAAO,CAAC,CAAC,CAAC,CAACT,KAAK,GAAGtB,SAAS,CAACa,OAAO,CAACU,UAAU;IAC3D,MAAMI,IAAI,GAAG,CAACD,CAAC,GAAGvB,MAAM,IAAI,CAAC;IAC7BH,SAAS,CAACa,OAAO,CAACR,UAAU,GAAGA,UAAU,GAAGsB,IAAI;EAClD,CAAC;EAED,MAAMM,cAAc,GAAGA,CAAA,KAAM;IAC3B/B,aAAa,CAAC,KAAK,CAAC;IACpB2B,UAAU,CAAC,MAAMrB,oBAAoB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EACpD,CAAC;EAED,oBACExC,OAAA;IAAKK,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCN,OAAA;MACEkE,GAAG,EAAElC,SAAU;MACf3B,SAAS,EAAC,8EAA8E;MACxFc,KAAK,EAAE;QACLgD,cAAc,EAAE,MAAM;QACtBC,eAAe,EAAE,MAAM;QACvBC,uBAAuB,EAAE;MAC3B,CAAE;MACFC,WAAW,EAAEjB,eAAgB;MAC7BkB,WAAW,EAAEf,eAAgB;MAC7BgB,SAAS,EAAEZ,aAAc;MACzBa,YAAY,EAAEb,aAAc;MAC5Bc,YAAY,EAAEZ,gBAAiB;MAC/Ba,WAAW,EAAEX,eAAgB;MAC7BY,UAAU,EAAEX,cAAe;MAAA3D,QAAA,EAE1BmC,aAAa,CAACoC,GAAG,CAAC,CAAC3E,IAAI,EAAE4E,KAAK,kBAC7B9E,OAAA;QAAiCK,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eAC7EN,OAAA,CAACC,QAAQ;UAACC,IAAI,EAAEA;QAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GADhB,GAAGT,IAAI,CAAC6E,EAAE,IAAID,KAAK,EAAE;QAAAtE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE1B,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNX,OAAA;MAAKK,SAAS,EAAC;IAAsG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxHX,OAAA;MAAKK,SAAS,EAAC;IAAuG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtH,CAAC;AAEV,CAAC;;AAED;AAAAoB,EAAA,CAzGMF,cAAc;AAAAmD,GAAA,GAAdnD,cAAc;AA0GpB,SAASoD,kBAAkBA,CAAA,EAAG;EAAAC,GAAA;EAC5B,MAAM;IAAEpD,KAAK;IAAEqD,OAAO;IAAEC;EAAM,CAAC,GAAGC,iBAAiB,CAAC,CAAC;EAErD,IAAIF,OAAO,EAAE,oBAAOnF,OAAA,CAACH,OAAO;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC/B,IAAIyE,KAAK,EAAE,oBAAOpF,OAAA;IAAKK,SAAS,EAAC,+BAA+B;IAAAC,QAAA,EAAE8E;EAAK;IAAA5E,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC9E,IAAI,CAACmB,KAAK,CAACY,MAAM,EAAE,oBAAO1C,OAAA;IAAKK,SAAS,EAAC,gCAAgC;IAAAC,QAAA,EAAC;EAAkB;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAElG,oBAAOX,OAAA,CAAC6B,cAAc;IAACC,KAAK,EAAEA;EAAM;IAAAtB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACzC;;AAEA;AAAAuE,GAAA,CAVSD,kBAAkB;AAAAK,GAAA,GAAlBL,kBAAkB;AAW3B,MAAM9D,KAAK,GAAGoE,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;AAC7CrE,KAAK,CAACsE,WAAW,GAAG;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACC,aAAa,CAAC,4BAA4B,CAAC,EAAE;EAC9DxE,KAAK,CAACyE,YAAY,CAAC,qBAAqB,EAAE,MAAM,CAAC;EACjDL,QAAQ,CAACG,IAAI,CAACG,WAAW,CAAC1E,KAAK,CAAC;AAClC;;AAEA;AACA,eAAe8D,kBAAkB;AAAC,IAAArD,EAAA,EAAAoD,GAAA,EAAAM,GAAA;AAAAQ,YAAA,CAAAlE,EAAA;AAAAkE,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAR,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}