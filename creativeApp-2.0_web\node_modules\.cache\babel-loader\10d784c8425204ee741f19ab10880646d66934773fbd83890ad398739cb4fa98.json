{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\dashboard\\\\ShiftSummarySection.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport Loading from \"../common/Loading\";\nimport { useShiftSummary } from \"../hooks/useTeamData\";\n\n// Remove the normalizeShift function as it's now handled in the hook\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SmallStat = ({\n  label,\n  value\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 px-4 py-3 flex items-center justify-between\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-sm font-medium text-gray-700 dark:text-gray-200\",\n    children: label\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center gap-2 text-gray-900 dark:text-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"\\uD83D\\uDC64\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"font-semibold\",\n      children: String(value).padStart(2, \"0\")\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 8,\n  columnNumber: 3\n}, this);\n_c = SmallStat;\nconst ShiftCard = ({\n  shift\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4\",\n  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n    className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3\",\n    children: [shift.name, \" Shift\"]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 sm:grid-cols-3 gap-3\",\n    children: [/*#__PURE__*/_jsxDEV(SmallStat, {\n      label: \"Total Designer\",\n      value: shift.designer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SmallStat, {\n      label: \"Total Developer\",\n      value: shift.developer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SmallStat, {\n      label: \"Total QA\",\n      value: shift.qa\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 18,\n  columnNumber: 3\n}, this);\n_c2 = ShiftCard;\nconst ShiftSummarySection = () => {\n  _s();\n  const [shifts, setShifts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [err, setErr] = useState(null);\n  useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    if (!token) {\n      setErr(\"No auth token found\");\n      setLoading(false);\n      return;\n    }\n    const load = async () => {\n      setLoading(true);\n      try {\n        const mockShifts = [{\n          name: \"Evening\",\n          stats: {\n            designer: 20,\n            developer: 25,\n            qa: 6\n          }\n        }, {\n          name: \"Morning\",\n          stats: {\n            designer: 20,\n            developer: 25,\n            qa: 6\n          }\n        }, {\n          name: \"Night\",\n          stats: {\n            designer: 20,\n            developer: 25,\n            qa: 6\n          }\n        }];\n        const normalized = mockShifts.map((item, idx) => normalizeShift(item, idx));\n        const order = [\"evening\", \"morning\", \"night\"];\n        normalized.sort((a, b) => order.indexOf((a.name || \"\").toLowerCase()) - order.indexOf((b.name || \"\").toLowerCase()));\n        setShifts(normalized);\n      } catch (error) {\n        console.error('Error:', error);\n        setErr('Unable to load shift data');\n      } finally {\n        setLoading(false);\n      }\n    };\n    load();\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 23\n  }, this);\n  if (err) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-red-500\",\n    children: err\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 19\n  }, this);\n  if (!shifts.length) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 lg:grid-cols-3 gap-4\",\n    children: shifts.map((s, i) => /*#__PURE__*/_jsxDEV(ShiftCard, {\n      shift: s\n    }, `${s.name}-${i}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_s(ShiftSummarySection, \"1PywyOufxRZWezkPB/1sGd7R4a4=\");\n_c3 = ShiftSummarySection;\nexport default ShiftSummarySection;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"SmallStat\");\n$RefreshReg$(_c2, \"ShiftCard\");\n$RefreshReg$(_c3, \"ShiftSummarySection\");", "map": {"version": 3, "names": ["React", "Loading", "useShiftSummary", "jsxDEV", "_jsxDEV", "SmallStat", "label", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "String", "padStart", "_c", "ShiftCard", "shift", "name", "designer", "developer", "qa", "_c2", "ShiftSummarySection", "_s", "shifts", "setShifts", "useState", "loading", "setLoading", "err", "setErr", "useEffect", "token", "localStorage", "getItem", "load", "mockShifts", "stats", "normalized", "map", "item", "idx", "normalizeShift", "order", "sort", "a", "b", "indexOf", "toLowerCase", "error", "console", "length", "s", "i", "_c3", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/dashboard/ShiftSummarySection.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport Loading from \"../common/Loading\";\r\nimport { useShiftSummary } from \"../hooks/useTeamData\";\r\n\r\n// Remove the normalizeShift function as it's now handled in the hook\r\n\r\nconst SmallStat = ({ label, value }) => (\r\n  <div className=\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 px-4 py-3 flex items-center justify-between\">\r\n    <div className=\"text-sm font-medium text-gray-700 dark:text-gray-200\">{label}</div>\r\n    <div className=\"flex items-center gap-2 text-gray-900 dark:text-gray-100\">\r\n      <span>👤</span>\r\n      <span className=\"font-semibold\">{String(value).padStart(2, \"0\")}</span>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst ShiftCard = ({ shift }) => (\r\n  <div className=\"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4\">\r\n    <h4 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3\">{shift.name} Shift</h4>\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-3\">\r\n      <SmallStat label=\"Total Designer\" value={shift.designer} />\r\n      <SmallStat label=\"Total Developer\" value={shift.developer} />\r\n      <SmallStat label=\"Total QA\" value={shift.qa} />\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst ShiftSummarySection = () => {\r\n  const [shifts, setShifts] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [err, setErr] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (!token) {\r\n      setErr(\"No auth token found\");\r\n      setLoading(false);\r\n      return;\r\n    }\r\n    const load = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const mockShifts = [\r\n          {\r\n            name: \"Evening\",\r\n            stats: {\r\n              designer: 20,\r\n              developer: 25,\r\n              qa: 6\r\n            }\r\n          },\r\n          {\r\n            name: \"Morning\",\r\n            stats: {\r\n              designer: 20,\r\n              developer: 25,\r\n              qa: 6\r\n            }\r\n          },\r\n          {\r\n            name: \"Night\",\r\n            stats: {\r\n              designer: 20,\r\n              developer: 25,\r\n              qa: 6\r\n            }\r\n          }\r\n        ];\r\n\r\n        const normalized = mockShifts.map((item, idx) => normalizeShift(item, idx));\r\n        const order = [\"evening\", \"morning\", \"night\"];\r\n        normalized.sort(\r\n          (a, b) => order.indexOf((a.name || \"\").toLowerCase()) - order.indexOf((b.name || \"\").toLowerCase())\r\n        );\r\n        setShifts(normalized);\r\n      } catch (error) {\r\n        console.error('Error:', error);\r\n        setErr('Unable to load shift data');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    load();\r\n  }, []);\r\n\r\n  if (loading) return <Loading />;\r\n  if (err) return <div className=\"text-red-500\">{err}</div>;\r\n  if (!shifts.length) return null;\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\r\n      {shifts.map((s, i) => (\r\n        <ShiftCard key={`${s.name}-${i}`} shift={s} />\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ShiftSummarySection;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,mBAAmB;AACvC,SAASC,eAAe,QAAQ,sBAAsB;;AAEtD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,SAAS,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAM,CAAC,kBACjCH,OAAA;EAAKI,SAAS,EAAC,mIAAmI;EAAAC,QAAA,gBAChJL,OAAA;IAAKI,SAAS,EAAC,sDAAsD;IAAAC,QAAA,EAAEH;EAAK;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC,eACnFT,OAAA;IAAKI,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBACvEL,OAAA;MAAAK,QAAA,EAAM;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACfT,OAAA;MAAMI,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEK,MAAM,CAACP,KAAK,CAAC,CAACQ,QAAQ,CAAC,CAAC,EAAE,GAAG;IAAC;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpE,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACG,EAAA,GARIX,SAAS;AAUf,MAAMY,SAAS,GAAGA,CAAC;EAAEC;AAAM,CAAC,kBAC1Bd,OAAA;EAAKI,SAAS,EAAC,uFAAuF;EAAAC,QAAA,gBACpGL,OAAA;IAAII,SAAS,EAAC,6DAA6D;IAAAC,QAAA,GAAES,KAAK,CAACC,IAAI,EAAC,QAAM;EAAA;IAAAT,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eACnGT,OAAA;IAAKI,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBACpDL,OAAA,CAACC,SAAS;MAACC,KAAK,EAAC,gBAAgB;MAACC,KAAK,EAAEW,KAAK,CAACE;IAAS;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC3DT,OAAA,CAACC,SAAS;MAACC,KAAK,EAAC,iBAAiB;MAACC,KAAK,EAAEW,KAAK,CAACG;IAAU;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7DT,OAAA,CAACC,SAAS;MAACC,KAAK,EAAC,UAAU;MAACC,KAAK,EAAEW,KAAK,CAACI;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5C,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACU,GAAA,GATIN,SAAS;AAWf,MAAMO,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGF,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACG,GAAG,EAAEC,MAAM,CAAC,GAAGJ,QAAQ,CAAC,IAAI,CAAC;EAEpCK,SAAS,CAAC,MAAM;IACd,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVF,MAAM,CAAC,qBAAqB,CAAC;MAC7BF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IACA,MAAMO,IAAI,GAAG,MAAAA,CAAA,KAAY;MACvBP,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMQ,UAAU,GAAG,CACjB;UACEnB,IAAI,EAAE,SAAS;UACfoB,KAAK,EAAE;YACLnB,QAAQ,EAAE,EAAE;YACZC,SAAS,EAAE,EAAE;YACbC,EAAE,EAAE;UACN;QACF,CAAC,EACD;UACEH,IAAI,EAAE,SAAS;UACfoB,KAAK,EAAE;YACLnB,QAAQ,EAAE,EAAE;YACZC,SAAS,EAAE,EAAE;YACbC,EAAE,EAAE;UACN;QACF,CAAC,EACD;UACEH,IAAI,EAAE,OAAO;UACboB,KAAK,EAAE;YACLnB,QAAQ,EAAE,EAAE;YACZC,SAAS,EAAE,EAAE;YACbC,EAAE,EAAE;UACN;QACF,CAAC,CACF;QAED,MAAMkB,UAAU,GAAGF,UAAU,CAACG,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAKC,cAAc,CAACF,IAAI,EAAEC,GAAG,CAAC,CAAC;QAC3E,MAAME,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;QAC7CL,UAAU,CAACM,IAAI,CACb,CAACC,CAAC,EAAEC,CAAC,KAAKH,KAAK,CAACI,OAAO,CAAC,CAACF,CAAC,CAAC5B,IAAI,IAAI,EAAE,EAAE+B,WAAW,CAAC,CAAC,CAAC,GAAGL,KAAK,CAACI,OAAO,CAAC,CAACD,CAAC,CAAC7B,IAAI,IAAI,EAAE,EAAE+B,WAAW,CAAC,CAAC,CACpG,CAAC;QACDvB,SAAS,CAACa,UAAU,CAAC;MACvB,CAAC,CAAC,OAAOW,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;QAC9BnB,MAAM,CAAC,2BAA2B,CAAC;MACrC,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDO,IAAI,CAAC,CAAC;EACR,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIR,OAAO,EAAE,oBAAOzB,OAAA,CAACH,OAAO;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC/B,IAAIkB,GAAG,EAAE,oBAAO3B,OAAA;IAAKI,SAAS,EAAC,cAAc;IAAAC,QAAA,EAAEsB;EAAG;IAAArB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACzD,IAAI,CAACa,MAAM,CAAC2B,MAAM,EAAE,OAAO,IAAI;EAE/B,oBACEjD,OAAA;IAAKI,SAAS,EAAC,uCAAuC;IAAAC,QAAA,EACnDiB,MAAM,CAACe,GAAG,CAAC,CAACa,CAAC,EAAEC,CAAC,kBACfnD,OAAA,CAACa,SAAS;MAAwBC,KAAK,EAAEoC;IAAE,GAA3B,GAAGA,CAAC,CAACnC,IAAI,IAAIoC,CAAC,EAAE;MAAA7C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAC9C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACY,EAAA,CArEID,mBAAmB;AAAAgC,GAAA,GAAnBhC,mBAAmB;AAuEzB,eAAeA,mBAAmB;AAAC,IAAAR,EAAA,EAAAO,GAAA,EAAAiC,GAAA;AAAAC,YAAA,CAAAzC,EAAA;AAAAyC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}