<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;
use App\Models\User;
use App\Models\Team;
use App\Models\Department;
use App\Models\Role;

class TeamApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $department;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user with admin role
        $adminRole = Role::create(['name' => 'admin']);
        $this->user = User::factory()->create();
        $this->user->roles()->attach($adminRole);
        
        // Create a test department
        $this->department = Department::factory()->create();
        
        // Fake storage for file uploads
        Storage::fake('public');
    }

    /** @test */
    public function it_can_create_a_team_with_workdays()
    {
        $iconFile = UploadedFile::fake()->image('icon.png');
        $logoFile = UploadedFile::fake()->image('logo.png');
        
        $workdays = ['Monday', 'Wednesday', 'Friday'];
        
        $response = $this->actingAs($this->user)
            ->postJson('/api/teams', [
                'name' => 'Test Team',
                'icon' => $iconFile,
                'logo' => $logoFile,
                'poc' => 'John Doe',
                'manager' => 'Jane Smith',
                'team_lead' => 'Bob Johnson',
                'workday' => json_encode($workdays),
                'launch' => '2025-01-01',
                'department_id' => $this->department->id,
            ]);

        $response->assertStatus(201);
        
        $team = Team::first();
        $this->assertEquals('Test Team', $team->name);
        $this->assertEquals($workdays, $team->workday);
        $this->assertEquals('John Doe', $team->poc);
        $this->assertEquals('Jane Smith', $team->manager);
        $this->assertEquals('Bob Johnson', $team->team_lead);
    }

    /** @test */
    public function it_validates_workday_format()
    {
        $iconFile = UploadedFile::fake()->image('icon.png');
        $logoFile = UploadedFile::fake()->image('logo.png');
        
        // Test with invalid workday format
        $response = $this->actingAs($this->user)
            ->postJson('/api/teams', [
                'name' => 'Test Team',
                'icon' => $iconFile,
                'logo' => $logoFile,
                'poc' => 'John Doe',
                'manager' => 'Jane Smith',
                'team_lead' => 'Bob Johnson',
                'workday' => 'invalid_json',
                'launch' => '2025-01-01',
                'department_id' => $this->department->id,
            ]);

        $response->assertStatus(422);
        $response->assertJsonStructure(['error']);
    }

    /** @test */
    public function it_validates_workday_values()
    {
        $iconFile = UploadedFile::fake()->image('icon.png');
        $logoFile = UploadedFile::fake()->image('logo.png');
        
        // Test with invalid day names
        $invalidWorkdays = ['Monday', 'InvalidDay', 'Friday'];
        
        $response = $this->actingAs($this->user)
            ->postJson('/api/teams', [
                'name' => 'Test Team',
                'icon' => $iconFile,
                'logo' => $logoFile,
                'poc' => 'John Doe',
                'manager' => 'Jane Smith',
                'team_lead' => 'Bob Johnson',
                'workday' => json_encode($invalidWorkdays),
                'launch' => '2025-01-01',
                'department_id' => $this->department->id,
            ]);

        $response->assertStatus(422);
        $response->assertJsonStructure(['error']);
    }

    /** @test */
    public function it_requires_at_least_one_workday()
    {
        $iconFile = UploadedFile::fake()->image('icon.png');
        $logoFile = UploadedFile::fake()->image('logo.png');
        
        // Test with empty workdays array
        $response = $this->actingAs($this->user)
            ->postJson('/api/teams', [
                'name' => 'Test Team',
                'icon' => $iconFile,
                'logo' => $logoFile,
                'poc' => 'John Doe',
                'manager' => 'Jane Smith',
                'team_lead' => 'Bob Johnson',
                'workday' => json_encode([]),
                'launch' => '2025-01-01',
                'department_id' => $this->department->id,
            ]);

        $response->assertStatus(422);
        $response->assertJsonStructure(['error']);
    }

    /** @test */
    public function it_can_update_team_workdays()
    {
        // Create a team first
        $team = Team::create([
            'name' => 'Test Team',
            'icon' => 'test-icon.png',
            'logo' => 'test-logo.png',
            'poc' => 'John Doe',
            'manager' => 'Jane Smith',
            'team_lead' => 'Bob Johnson',
            'workday' => ['Monday', 'Tuesday'],
            'launch' => '2025-01-01',
            'created_by' => $this->user->id,
        ]);

        $newWorkdays = ['Wednesday', 'Thursday', 'Friday'];
        
        $response = $this->actingAs($this->user)
            ->putJson("/api/teams/{$team->id}", [
                'name' => 'Updated Test Team',
                'poc' => 'John Doe Updated',
                'manager' => 'Jane Smith Updated',
                'team_lead' => 'Bob Johnson Updated',
                'workday' => json_encode($newWorkdays),
                'launch' => '2025-02-01',
                'department_id' => $this->department->id,
            ]);

        $response->assertStatus(200);
        
        $team->refresh();
        $this->assertEquals('Updated Test Team', $team->name);
        $this->assertEquals($newWorkdays, $team->workday);
        $this->assertEquals('John Doe Updated', $team->poc);
    }

    /** @test */
    public function it_returns_workdays_as_array_in_api_response()
    {
        $workdays = ['Monday', 'Wednesday', 'Friday'];
        
        $team = Team::create([
            'name' => 'Test Team',
            'icon' => 'test-icon.png',
            'logo' => 'test-logo.png',
            'poc' => 'John Doe',
            'manager' => 'Jane Smith',
            'team_lead' => 'Bob Johnson',
            'workday' => $workdays,
            'launch' => '2025-01-01',
            'created_by' => $this->user->id,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/teams/{$team->id}");

        $response->assertStatus(200);
        $response->assertJson([
            'team' => [
                'workday' => $workdays
            ]
        ]);
    }
}
