{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useGetTeamsListQuery, useGetUsersByDefaultTeamListQuery, useGetShiftsListQuery } from '../features/api';\nconst isTokenValid = () => {\n  const token = localStorage.getItem('token');\n  return token && token.length > 0;\n};\nexport const useTeamData = () => {\n  _s();\n  const [teamLead, setTeamLead] = useState('');\n  const [totalMembers, setTotalMembers] = useState(0);\n  const [billableHours, setBillableHours] = useState(0);\n\n  // Use existing API hooks\n  const {\n    data: teamsData\n  } = useGetTeamsListQuery();\n  const {\n    data: usersData\n  } = useGetUsersByDefaultTeamListQuery();\n  useEffect(() => {\n    if (!isTokenValid() || !teamsData || !usersData) return;\n    try {\n      var _currentUser$teams, _currentUser$teams$;\n      // Get current user's team information\n      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');\n      const userTeamId = currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$teams = currentUser.teams) === null || _currentUser$teams === void 0 ? void 0 : (_currentUser$teams$ = _currentUser$teams[0]) === null || _currentUser$teams$ === void 0 ? void 0 : _currentUser$teams$.id;\n      if (userTeamId && teamsData && usersData) {\n        // Find team lead and calculate team stats\n        const teamMembers = usersData.filter(user => user.team_id === userTeamId);\n\n        // Find team lead (assuming first user or user with specific role)\n        const lead = teamMembers.find(member => {\n          var _member$designations, _member$designations$, _member$designations$2, _member$designations2, _member$designations3, _member$designations4;\n          return ((_member$designations = member.designations) === null || _member$designations === void 0 ? void 0 : (_member$designations$ = _member$designations[0]) === null || _member$designations$ === void 0 ? void 0 : (_member$designations$2 = _member$designations$.name) === null || _member$designations$2 === void 0 ? void 0 : _member$designations$2.toLowerCase().includes('lead')) || ((_member$designations2 = member.designations) === null || _member$designations2 === void 0 ? void 0 : (_member$designations3 = _member$designations2[0]) === null || _member$designations3 === void 0 ? void 0 : (_member$designations4 = _member$designations3.name) === null || _member$designations4 === void 0 ? void 0 : _member$designations4.toLowerCase().includes('manager'));\n        }) || teamMembers[0];\n        setTeamLead(lead ? `${lead.fname || ''} ${lead.lname || ''}`.trim() : 'N/A');\n        setTotalMembers(teamMembers.length);\n        // Mock billable hours calculation - you can replace with real calculation\n        setBillableHours(teamMembers.length * 8);\n      }\n    } catch (error) {\n      console.error('Error processing team data:', error);\n    }\n  }, [teamsData, usersData]);\n  return {\n    teamLead,\n    totalMembers,\n    billableHours\n  };\n};\n\n// New hook for dashboard teams data\n_s(useTeamData, \"WZ5zlM8P4SLR8c48XTpv/Woinio=\", false, function () {\n  return [useGetTeamsListQuery, useGetUsersByDefaultTeamListQuery];\n});\nexport const useDashboardTeams = () => {\n  _s2();\n  const [teams, setTeams] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const {\n    data: teamsData,\n    isLoading: teamsLoading,\n    error: teamsError\n  } = useGetTeamsListQuery();\n  const {\n    data: usersData,\n    isLoading: usersLoading,\n    error: usersError\n  } = useGetUsersByDefaultTeamListQuery();\n\n  // Fallback mock data for development/testing\n  const mockTeams = [{\n    id: 1,\n    name: \"AccuWeather\",\n    teamLead: \"Kamal Hossain\",\n    totalMembers: 10,\n    billableHours: 80,\n    logo: \"/assets/client-logos/accuweather.png\",\n    isTopClient: true,\n    billingStatus: \"Active\",\n    shift: \"Morning\",\n    billingRate: 75\n  }, {\n    id: 2,\n    name: \"Bloomberg\",\n    teamLead: \"Kamal Hossain\",\n    totalMembers: 8,\n    billableHours: 64,\n    logo: \"/assets/client-logos/bloomberg.png\",\n    isTopClient: true,\n    billingStatus: \"Active\",\n    shift: \"Evening\",\n    billingRate: 85\n  }, {\n    id: 3,\n    name: \"Boats Group\",\n    teamLead: \"Aminul Islam\",\n    totalMembers: 13,\n    billableHours: 104,\n    logo: \"/assets/client-logos/boats-group.png\",\n    isTopClient: true,\n    billingStatus: \"Active\",\n    shift: \"Morning\",\n    billingRate: 70\n  }, {\n    id: 4,\n    name: \"Clipcentric\",\n    teamLead: \"Aminul Islam\",\n    totalMembers: 15,\n    billableHours: 120,\n    logo: \"/assets/client-logos/clipcentric.png\",\n    isTopClient: false,\n    billingStatus: \"Active\",\n    shift: \"Night\",\n    billingRate: 65\n  }, {\n    id: 5,\n    name: \"MultiView\",\n    teamLead: \"Hasan Ahmed\",\n    totalMembers: 5,\n    billableHours: 40,\n    logo: \"/assets/client-logos/multiview.png\",\n    isTopClient: false,\n    billingStatus: \"Active\",\n    shift: \"Evening\",\n    billingRate: 60\n  }, {\n    id: 6,\n    name: \"Bigtincan\",\n    teamLead: \"Nafiul Islam\",\n    totalMembers: 5,\n    billableHours: 40,\n    logo: \"/assets/client-logos/bigtincan.png\",\n    isTopClient: false,\n    billingStatus: \"Active\",\n    shift: \"Morning\",\n    billingRate: 55\n  }];\n  useEffect(() => {\n    if (teamsLoading || usersLoading) {\n      setLoading(true);\n      return;\n    }\n    if (teamsError || usersError) {\n      setError('Failed to load team data');\n      setLoading(false);\n      return;\n    }\n    if (teamsData && usersData && teamsData.length > 0) {\n      try {\n        const processedTeams = teamsData.map(team => {\n          const teamMembers = usersData.filter(user => user.team_id === team.id);\n          const teamLead = teamMembers.find(member => {\n            var _member$designations5, _member$designations6, _member$designations7, _member$designations8, _member$designations9, _member$designations10;\n            return ((_member$designations5 = member.designations) === null || _member$designations5 === void 0 ? void 0 : (_member$designations6 = _member$designations5[0]) === null || _member$designations6 === void 0 ? void 0 : (_member$designations7 = _member$designations6.name) === null || _member$designations7 === void 0 ? void 0 : _member$designations7.toLowerCase().includes('lead')) || ((_member$designations8 = member.designations) === null || _member$designations8 === void 0 ? void 0 : (_member$designations9 = _member$designations8[0]) === null || _member$designations9 === void 0 ? void 0 : (_member$designations10 = _member$designations9.name) === null || _member$designations10 === void 0 ? void 0 : _member$designations10.toLowerCase().includes('manager'));\n          }) || teamMembers[0];\n          return {\n            id: team.id,\n            name: team.name,\n            teamLead: teamLead ? `${teamLead.fname || ''} ${teamLead.lname || ''}`.trim() : 'N/A',\n            totalMembers: teamMembers.length,\n            billableHours: teamMembers.length * 8,\n            // Mock calculation\n            logo: `/assets/client-logos/${team.name.toLowerCase().replace(/\\s+/g, '-')}.png`,\n            isTopClient: Math.random() > 0.5,\n            // Mock priority status\n            billingStatus: 'Active',\n            shift: 'Morning',\n            // Default shift\n            billingRate: 50 + Math.floor(Math.random() * 100)\n          };\n        });\n        setTeams(processedTeams.length > 0 ? processedTeams : mockTeams);\n        setError(null);\n      } catch (err) {\n        console.error('Error processing teams data:', err);\n        setTeams(mockTeams); // Fallback to mock data\n        setError(null); // Don't show error, just use fallback\n      }\n    } else if (!teamsLoading && !usersLoading) {\n      // If no data available, use mock data\n      setTeams(mockTeams);\n      setError(null);\n    }\n    setLoading(false);\n  }, [teamsData, usersData, teamsLoading, usersLoading, teamsError, usersError]);\n  return {\n    teams,\n    loading,\n    error\n  };\n};\n\n// Hook for shift summary data\n_s2(useDashboardTeams, \"+ELNcgI1NtDTY8a8sJfigF/JPGI=\", false, function () {\n  return [useGetTeamsListQuery, useGetUsersByDefaultTeamListQuery];\n});\nexport const useShiftSummary = () => {\n  _s3();\n  const [shifts, setShifts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const {\n    data: shiftsData,\n    isLoading: shiftsLoading,\n    error: shiftsError\n  } = useGetShiftsListQuery();\n  const {\n    data: usersData,\n    isLoading: usersLoading,\n    error: usersError\n  } = useGetUsersByDefaultTeamListQuery();\n  useEffect(() => {\n    if (shiftsLoading || usersLoading) {\n      setLoading(true);\n      return;\n    }\n    if (shiftsError || usersError) {\n      setError('Failed to load shift data');\n      setLoading(false);\n      return;\n    }\n    if (shiftsData && usersData) {\n      try {\n        const processedShifts = shiftsData.map(shift => {\n          // Mock calculation - in real scenario, you'd filter users by shift\n          const shiftUsers = usersData.filter(() => Math.random() > 0.7); // Mock filter\n\n          const designerCount = shiftUsers.filter(user => {\n            var _user$designations, _user$designations$, _user$designations$$n;\n            return (_user$designations = user.designations) === null || _user$designations === void 0 ? void 0 : (_user$designations$ = _user$designations[0]) === null || _user$designations$ === void 0 ? void 0 : (_user$designations$$n = _user$designations$.name) === null || _user$designations$$n === void 0 ? void 0 : _user$designations$$n.toLowerCase().includes('designer');\n          }).length || Math.floor(Math.random() * 25) + 15;\n          const developerCount = shiftUsers.filter(user => {\n            var _user$designations2, _user$designations2$, _user$designations2$$;\n            return (_user$designations2 = user.designations) === null || _user$designations2 === void 0 ? void 0 : (_user$designations2$ = _user$designations2[0]) === null || _user$designations2$ === void 0 ? void 0 : (_user$designations2$$ = _user$designations2$.name) === null || _user$designations2$$ === void 0 ? void 0 : _user$designations2$$.toLowerCase().includes('developer');\n          }).length || Math.floor(Math.random() * 30) + 20;\n          const qaCount = shiftUsers.filter(user => {\n            var _user$designations3, _user$designations3$, _user$designations3$$, _user$designations4, _user$designations4$, _user$designations4$$;\n            return ((_user$designations3 = user.designations) === null || _user$designations3 === void 0 ? void 0 : (_user$designations3$ = _user$designations3[0]) === null || _user$designations3$ === void 0 ? void 0 : (_user$designations3$$ = _user$designations3$.name) === null || _user$designations3$$ === void 0 ? void 0 : _user$designations3$$.toLowerCase().includes('qa')) || ((_user$designations4 = user.designations) === null || _user$designations4 === void 0 ? void 0 : (_user$designations4$ = _user$designations4[0]) === null || _user$designations4$ === void 0 ? void 0 : (_user$designations4$$ = _user$designations4$.name) === null || _user$designations4$$ === void 0 ? void 0 : _user$designations4$$.toLowerCase().includes('quality'));\n          }).length || Math.floor(Math.random() * 10) + 5;\n          return {\n            name: shift.shift_name || 'Unknown Shift',\n            designer: designerCount,\n            developer: developerCount,\n            qa: qaCount\n          };\n        });\n\n        // Ensure we have the three main shifts\n        const defaultShifts = [{\n          name: 'Evening',\n          designer: 20,\n          developer: 25,\n          qa: 6\n        }, {\n          name: 'Morning',\n          designer: 20,\n          developer: 25,\n          qa: 6\n        }, {\n          name: 'Night',\n          designer: 20,\n          developer: 25,\n          qa: 6\n        }];\n        const finalShifts = processedShifts.length > 0 ? processedShifts : defaultShifts;\n\n        // Sort by preferred order\n        const order = ['evening', 'morning', 'night'];\n        finalShifts.sort((a, b) => order.indexOf(a.name.toLowerCase()) - order.indexOf(b.name.toLowerCase()));\n        setShifts(finalShifts);\n        setError(null);\n      } catch (err) {\n        console.error('Error processing shifts data:', err);\n        setError('Error processing shift data');\n      }\n    }\n    setLoading(false);\n  }, [shiftsData, usersData, shiftsLoading, usersLoading, shiftsError, usersError]);\n  return {\n    shifts,\n    loading,\n    error\n  };\n};\n_s3(useShiftSummary, \"nXpKlla9XkICHpthR3mzW2VEpDk=\", false, function () {\n  return [useGetShiftsListQuery, useGetUsersByDefaultTeamListQuery];\n});", "map": {"version": 3, "names": ["useState", "useEffect", "useGetTeamsListQuery", "useGetUsersByDefaultTeamListQuery", "useGetShiftsListQuery", "isTokenValid", "token", "localStorage", "getItem", "length", "useTeamData", "_s", "teamLead", "setTeamLead", "totalMembers", "setTotalMembers", "billableHours", "setBillableHours", "data", "teamsData", "usersData", "_currentUser$teams", "_currentUser$teams$", "currentUser", "JSON", "parse", "userTeamId", "teams", "id", "teamMembers", "filter", "user", "team_id", "lead", "find", "member", "_member$designations", "_member$designations$", "_member$designations$2", "_member$designations2", "_member$designations3", "_member$designations4", "designations", "name", "toLowerCase", "includes", "fname", "lname", "trim", "error", "console", "useDashboardTeams", "_s2", "setTeams", "loading", "setLoading", "setError", "isLoading", "teamsLoading", "teamsError", "usersLoading", "usersError", "mockTeams", "logo", "isTopClient", "billingStatus", "shift", "billingRate", "processedTeams", "map", "team", "_member$designations5", "_member$designations6", "_member$designations7", "_member$designations8", "_member$designations9", "_member$designations10", "replace", "Math", "random", "floor", "err", "useShiftSummary", "_s3", "shifts", "setShifts", "shiftsData", "shiftsLoading", "shiftsError", "processedShifts", "shiftUsers", "designerCount", "_user$designations", "_user$designations$", "_user$designations$$n", "developerCount", "_user$designations2", "_user$designations2$", "_user$designations2$$", "qaCount", "_user$designations3", "_user$designations3$", "_user$designations3$$", "_user$designations4", "_user$designations4$", "_user$designations4$$", "shift_name", "designer", "developer", "qa", "defaultShifts", "finalShifts", "order", "sort", "a", "b", "indexOf"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/hooks/useTeamData.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { useGetTeamsListQuery, useGetUsersByDefaultTeamListQuery, useGetShiftsListQuery } from '../features/api';\r\n\r\nconst isTokenValid = () => {\r\n  const token = localStorage.getItem('token');\r\n  return token && token.length > 0;\r\n};\r\n\r\nexport const useTeamData = () => {\r\n  const [teamLead, setTeamLead] = useState('');\r\n  const [totalMembers, setTotalMembers] = useState(0);\r\n  const [billableHours, setBillableHours] = useState(0);\r\n\r\n  // Use existing API hooks\r\n  const { data: teamsData } = useGetTeamsListQuery();\r\n  const { data: usersData } = useGetUsersByDefaultTeamListQuery();\r\n\r\n  useEffect(() => {\r\n    if (!isTokenValid() || !teamsData || !usersData) return;\r\n\r\n    try {\r\n      // Get current user's team information\r\n      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');\r\n      const userTeamId = currentUser?.teams?.[0]?.id;\r\n\r\n      if (userTeamId && teamsData && usersData) {\r\n        // Find team lead and calculate team stats\r\n        const teamMembers = usersData.filter(user => user.team_id === userTeamId);\r\n\r\n        // Find team lead (assuming first user or user with specific role)\r\n        const lead = teamMembers.find(member =>\r\n          member.designations?.[0]?.name?.toLowerCase().includes('lead') ||\r\n          member.designations?.[0]?.name?.toLowerCase().includes('manager')\r\n        ) || teamMembers[0];\r\n\r\n        setTeamLead(lead ? `${lead.fname || ''} ${lead.lname || ''}`.trim() : 'N/A');\r\n        setTotalMembers(teamMembers.length);\r\n        // Mock billable hours calculation - you can replace with real calculation\r\n        setBillableHours(teamMembers.length * 8);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error processing team data:', error);\r\n    }\r\n  }, [teamsData, usersData]);\r\n\r\n  return { teamLead, totalMembers, billableHours };\r\n};\r\n\r\n// New hook for dashboard teams data\r\nexport const useDashboardTeams = () => {\r\n  const [teams, setTeams] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  const { data: teamsData, isLoading: teamsLoading, error: teamsError } = useGetTeamsListQuery();\r\n  const { data: usersData, isLoading: usersLoading, error: usersError } = useGetUsersByDefaultTeamListQuery();\r\n\r\n  // Fallback mock data for development/testing\r\n  const mockTeams = [\r\n    { id: 1, name: \"AccuWeather\", teamLead: \"Kamal Hossain\", totalMembers: 10, billableHours: 80, logo: \"/assets/client-logos/accuweather.png\", isTopClient: true, billingStatus: \"Active\", shift: \"Morning\", billingRate: 75 },\r\n    { id: 2, name: \"Bloomberg\", teamLead: \"Kamal Hossain\", totalMembers: 8, billableHours: 64, logo: \"/assets/client-logos/bloomberg.png\", isTopClient: true, billingStatus: \"Active\", shift: \"Evening\", billingRate: 85 },\r\n    { id: 3, name: \"Boats Group\", teamLead: \"Aminul Islam\", totalMembers: 13, billableHours: 104, logo: \"/assets/client-logos/boats-group.png\", isTopClient: true, billingStatus: \"Active\", shift: \"Morning\", billingRate: 70 },\r\n    { id: 4, name: \"Clipcentric\", teamLead: \"Aminul Islam\", totalMembers: 15, billableHours: 120, logo: \"/assets/client-logos/clipcentric.png\", isTopClient: false, billingStatus: \"Active\", shift: \"Night\", billingRate: 65 },\r\n    { id: 5, name: \"MultiView\", teamLead: \"Hasan Ahmed\", totalMembers: 5, billableHours: 40, logo: \"/assets/client-logos/multiview.png\", isTopClient: false, billingStatus: \"Active\", shift: \"Evening\", billingRate: 60 },\r\n    { id: 6, name: \"Bigtincan\", teamLead: \"Nafiul Islam\", totalMembers: 5, billableHours: 40, logo: \"/assets/client-logos/bigtincan.png\", isTopClient: false, billingStatus: \"Active\", shift: \"Morning\", billingRate: 55 }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    if (teamsLoading || usersLoading) {\r\n      setLoading(true);\r\n      return;\r\n    }\r\n\r\n    if (teamsError || usersError) {\r\n      setError('Failed to load team data');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    if (teamsData && usersData && teamsData.length > 0) {\r\n      try {\r\n        const processedTeams = teamsData.map(team => {\r\n          const teamMembers = usersData.filter(user => user.team_id === team.id);\r\n          const teamLead = teamMembers.find(member =>\r\n            member.designations?.[0]?.name?.toLowerCase().includes('lead') ||\r\n            member.designations?.[0]?.name?.toLowerCase().includes('manager')\r\n          ) || teamMembers[0];\r\n\r\n          return {\r\n            id: team.id,\r\n            name: team.name,\r\n            teamLead: teamLead ? `${teamLead.fname || ''} ${teamLead.lname || ''}`.trim() : 'N/A',\r\n            totalMembers: teamMembers.length,\r\n            billableHours: teamMembers.length * 8, // Mock calculation\r\n            logo: `/assets/client-logos/${team.name.toLowerCase().replace(/\\s+/g, '-')}.png`,\r\n            isTopClient: Math.random() > 0.5, // Mock priority status\r\n            billingStatus: 'Active',\r\n            shift: 'Morning', // Default shift\r\n            billingRate: 50 + Math.floor(Math.random() * 100)\r\n          };\r\n        });\r\n\r\n        setTeams(processedTeams.length > 0 ? processedTeams : mockTeams);\r\n        setError(null);\r\n      } catch (err) {\r\n        console.error('Error processing teams data:', err);\r\n        setTeams(mockTeams); // Fallback to mock data\r\n        setError(null); // Don't show error, just use fallback\r\n      }\r\n    } else if (!teamsLoading && !usersLoading) {\r\n      // If no data available, use mock data\r\n      setTeams(mockTeams);\r\n      setError(null);\r\n    }\r\n\r\n    setLoading(false);\r\n  }, [teamsData, usersData, teamsLoading, usersLoading, teamsError, usersError]);\r\n\r\n  return { teams, loading, error };\r\n};\r\n\r\n// Hook for shift summary data\r\nexport const useShiftSummary = () => {\r\n  const [shifts, setShifts] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  const { data: shiftsData, isLoading: shiftsLoading, error: shiftsError } = useGetShiftsListQuery();\r\n  const { data: usersData, isLoading: usersLoading, error: usersError } = useGetUsersByDefaultTeamListQuery();\r\n\r\n  useEffect(() => {\r\n    if (shiftsLoading || usersLoading) {\r\n      setLoading(true);\r\n      return;\r\n    }\r\n\r\n    if (shiftsError || usersError) {\r\n      setError('Failed to load shift data');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    if (shiftsData && usersData) {\r\n      try {\r\n        const processedShifts = shiftsData.map(shift => {\r\n          // Mock calculation - in real scenario, you'd filter users by shift\r\n          const shiftUsers = usersData.filter(() => Math.random() > 0.7); // Mock filter\r\n\r\n          const designerCount = shiftUsers.filter(user =>\r\n            user.designations?.[0]?.name?.toLowerCase().includes('designer')\r\n          ).length || Math.floor(Math.random() * 25) + 15;\r\n\r\n          const developerCount = shiftUsers.filter(user =>\r\n            user.designations?.[0]?.name?.toLowerCase().includes('developer')\r\n          ).length || Math.floor(Math.random() * 30) + 20;\r\n\r\n          const qaCount = shiftUsers.filter(user =>\r\n            user.designations?.[0]?.name?.toLowerCase().includes('qa') ||\r\n            user.designations?.[0]?.name?.toLowerCase().includes('quality')\r\n          ).length || Math.floor(Math.random() * 10) + 5;\r\n\r\n          return {\r\n            name: shift.shift_name || 'Unknown Shift',\r\n            designer: designerCount,\r\n            developer: developerCount,\r\n            qa: qaCount\r\n          };\r\n        });\r\n\r\n        // Ensure we have the three main shifts\r\n        const defaultShifts = [\r\n          { name: 'Evening', designer: 20, developer: 25, qa: 6 },\r\n          { name: 'Morning', designer: 20, developer: 25, qa: 6 },\r\n          { name: 'Night', designer: 20, developer: 25, qa: 6 }\r\n        ];\r\n\r\n        const finalShifts = processedShifts.length > 0 ? processedShifts : defaultShifts;\r\n\r\n        // Sort by preferred order\r\n        const order = ['evening', 'morning', 'night'];\r\n        finalShifts.sort((a, b) =>\r\n          order.indexOf(a.name.toLowerCase()) - order.indexOf(b.name.toLowerCase())\r\n        );\r\n\r\n        setShifts(finalShifts);\r\n        setError(null);\r\n      } catch (err) {\r\n        console.error('Error processing shifts data:', err);\r\n        setError('Error processing shift data');\r\n      }\r\n    }\r\n\r\n    setLoading(false);\r\n  }, [shiftsData, usersData, shiftsLoading, usersLoading, shiftsError, usersError]);\r\n\r\n  return { shifts, loading, error };\r\n};"], "mappings": ";;;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,oBAAoB,EAAEC,iCAAiC,EAAEC,qBAAqB,QAAQ,iBAAiB;AAEhH,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,OAAOF,KAAK,IAAIA,KAAK,CAACG,MAAM,GAAG,CAAC;AAClC,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC;;EAErD;EACA,MAAM;IAAEkB,IAAI,EAAEC;EAAU,CAAC,GAAGjB,oBAAoB,CAAC,CAAC;EAClD,MAAM;IAAEgB,IAAI,EAAEE;EAAU,CAAC,GAAGjB,iCAAiC,CAAC,CAAC;EAE/DF,SAAS,CAAC,MAAM;IACd,IAAI,CAACI,YAAY,CAAC,CAAC,IAAI,CAACc,SAAS,IAAI,CAACC,SAAS,EAAE;IAEjD,IAAI;MAAA,IAAAC,kBAAA,EAAAC,mBAAA;MACF;MACA,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAClB,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;MACpE,MAAMkB,UAAU,GAAGH,WAAW,aAAXA,WAAW,wBAAAF,kBAAA,GAAXE,WAAW,CAAEI,KAAK,cAAAN,kBAAA,wBAAAC,mBAAA,GAAlBD,kBAAA,CAAqB,CAAC,CAAC,cAAAC,mBAAA,uBAAvBA,mBAAA,CAAyBM,EAAE;MAE9C,IAAIF,UAAU,IAAIP,SAAS,IAAIC,SAAS,EAAE;QACxC;QACA,MAAMS,WAAW,GAAGT,SAAS,CAACU,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKN,UAAU,CAAC;;QAEzE;QACA,MAAMO,IAAI,GAAGJ,WAAW,CAACK,IAAI,CAACC,MAAM;UAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;UAAA,OAClC,EAAAL,oBAAA,GAAAD,MAAM,CAACO,YAAY,cAAAN,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAsB,CAAC,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BM,IAAI,cAAAL,sBAAA,uBAA9BA,sBAAA,CAAgCM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,OAAAN,qBAAA,GAC9DJ,MAAM,CAACO,YAAY,cAAAH,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAsB,CAAC,CAAC,cAAAC,qBAAA,wBAAAC,qBAAA,GAAxBD,qBAAA,CAA0BG,IAAI,cAAAF,qBAAA,uBAA9BA,qBAAA,CAAgCG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,CAAC;QAAA,CACnE,CAAC,IAAIhB,WAAW,CAAC,CAAC,CAAC;QAEnBhB,WAAW,CAACoB,IAAI,GAAG,GAAGA,IAAI,CAACa,KAAK,IAAI,EAAE,IAAIb,IAAI,CAACc,KAAK,IAAI,EAAE,EAAE,CAACC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;QAC5EjC,eAAe,CAACc,WAAW,CAACpB,MAAM,CAAC;QACnC;QACAQ,gBAAgB,CAACY,WAAW,CAACpB,MAAM,GAAG,CAAC,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC,EAAE,CAAC9B,SAAS,EAAEC,SAAS,CAAC,CAAC;EAE1B,OAAO;IAAER,QAAQ;IAAEE,YAAY;IAAEE;EAAc,CAAC;AAClD,CAAC;;AAED;AAAAL,EAAA,CAxCaD,WAAW;EAAA,QAMMR,oBAAoB,EACpBC,iCAAiC;AAAA;AAkC/D,OAAO,MAAMgD,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACrC,MAAM,CAACzB,KAAK,EAAE0B,QAAQ,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiD,KAAK,EAAEO,QAAQ,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM;IAAEkB,IAAI,EAAEC,SAAS;IAAEsC,SAAS,EAAEC,YAAY;IAAET,KAAK,EAAEU;EAAW,CAAC,GAAGzD,oBAAoB,CAAC,CAAC;EAC9F,MAAM;IAAEgB,IAAI,EAAEE,SAAS;IAAEqC,SAAS,EAAEG,YAAY;IAAEX,KAAK,EAAEY;EAAW,CAAC,GAAG1D,iCAAiC,CAAC,CAAC;;EAE3G;EACA,MAAM2D,SAAS,GAAG,CAChB;IAAElC,EAAE,EAAE,CAAC;IAAEe,IAAI,EAAE,aAAa;IAAE/B,QAAQ,EAAE,eAAe;IAAEE,YAAY,EAAE,EAAE;IAAEE,aAAa,EAAE,EAAE;IAAE+C,IAAI,EAAE,sCAAsC;IAAEC,WAAW,EAAE,IAAI;IAAEC,aAAa,EAAE,QAAQ;IAAEC,KAAK,EAAE,SAAS;IAAEC,WAAW,EAAE;EAAG,CAAC,EAC3N;IAAEvC,EAAE,EAAE,CAAC;IAAEe,IAAI,EAAE,WAAW;IAAE/B,QAAQ,EAAE,eAAe;IAAEE,YAAY,EAAE,CAAC;IAAEE,aAAa,EAAE,EAAE;IAAE+C,IAAI,EAAE,oCAAoC;IAAEC,WAAW,EAAE,IAAI;IAAEC,aAAa,EAAE,QAAQ;IAAEC,KAAK,EAAE,SAAS;IAAEC,WAAW,EAAE;EAAG,CAAC,EACtN;IAAEvC,EAAE,EAAE,CAAC;IAAEe,IAAI,EAAE,aAAa;IAAE/B,QAAQ,EAAE,cAAc;IAAEE,YAAY,EAAE,EAAE;IAAEE,aAAa,EAAE,GAAG;IAAE+C,IAAI,EAAE,sCAAsC;IAAEC,WAAW,EAAE,IAAI;IAAEC,aAAa,EAAE,QAAQ;IAAEC,KAAK,EAAE,SAAS;IAAEC,WAAW,EAAE;EAAG,CAAC,EAC3N;IAAEvC,EAAE,EAAE,CAAC;IAAEe,IAAI,EAAE,aAAa;IAAE/B,QAAQ,EAAE,cAAc;IAAEE,YAAY,EAAE,EAAE;IAAEE,aAAa,EAAE,GAAG;IAAE+C,IAAI,EAAE,sCAAsC;IAAEC,WAAW,EAAE,KAAK;IAAEC,aAAa,EAAE,QAAQ;IAAEC,KAAK,EAAE,OAAO;IAAEC,WAAW,EAAE;EAAG,CAAC,EAC1N;IAAEvC,EAAE,EAAE,CAAC;IAAEe,IAAI,EAAE,WAAW;IAAE/B,QAAQ,EAAE,aAAa;IAAEE,YAAY,EAAE,CAAC;IAAEE,aAAa,EAAE,EAAE;IAAE+C,IAAI,EAAE,oCAAoC;IAAEC,WAAW,EAAE,KAAK;IAAEC,aAAa,EAAE,QAAQ;IAAEC,KAAK,EAAE,SAAS;IAAEC,WAAW,EAAE;EAAG,CAAC,EACrN;IAAEvC,EAAE,EAAE,CAAC;IAAEe,IAAI,EAAE,WAAW;IAAE/B,QAAQ,EAAE,cAAc;IAAEE,YAAY,EAAE,CAAC;IAAEE,aAAa,EAAE,EAAE;IAAE+C,IAAI,EAAE,oCAAoC;IAAEC,WAAW,EAAE,KAAK;IAAEC,aAAa,EAAE,QAAQ;IAAEC,KAAK,EAAE,SAAS;IAAEC,WAAW,EAAE;EAAG,CAAC,CACvN;EAEDlE,SAAS,CAAC,MAAM;IACd,IAAIyD,YAAY,IAAIE,YAAY,EAAE;MAChCL,UAAU,CAAC,IAAI,CAAC;MAChB;IACF;IAEA,IAAII,UAAU,IAAIE,UAAU,EAAE;MAC5BL,QAAQ,CAAC,0BAA0B,CAAC;MACpCD,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAIpC,SAAS,IAAIC,SAAS,IAAID,SAAS,CAACV,MAAM,GAAG,CAAC,EAAE;MAClD,IAAI;QACF,MAAM2D,cAAc,GAAGjD,SAAS,CAACkD,GAAG,CAACC,IAAI,IAAI;UAC3C,MAAMzC,WAAW,GAAGT,SAAS,CAACU,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKsC,IAAI,CAAC1C,EAAE,CAAC;UACtE,MAAMhB,QAAQ,GAAGiB,WAAW,CAACK,IAAI,CAACC,MAAM;YAAA,IAAAoC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;YAAA,OACtC,EAAAL,qBAAA,GAAApC,MAAM,CAACO,YAAY,cAAA6B,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAsB,CAAC,CAAC,cAAAC,qBAAA,wBAAAC,qBAAA,GAAxBD,qBAAA,CAA0B7B,IAAI,cAAA8B,qBAAA,uBAA9BA,qBAAA,CAAgC7B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,OAAA6B,qBAAA,GAC9DvC,MAAM,CAACO,YAAY,cAAAgC,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAsB,CAAC,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BhC,IAAI,cAAAiC,sBAAA,uBAA9BA,sBAAA,CAAgChC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,CAAC;UAAA,CACnE,CAAC,IAAIhB,WAAW,CAAC,CAAC,CAAC;UAEnB,OAAO;YACLD,EAAE,EAAE0C,IAAI,CAAC1C,EAAE;YACXe,IAAI,EAAE2B,IAAI,CAAC3B,IAAI;YACf/B,QAAQ,EAAEA,QAAQ,GAAG,GAAGA,QAAQ,CAACkC,KAAK,IAAI,EAAE,IAAIlC,QAAQ,CAACmC,KAAK,IAAI,EAAE,EAAE,CAACC,IAAI,CAAC,CAAC,GAAG,KAAK;YACrFlC,YAAY,EAAEe,WAAW,CAACpB,MAAM;YAChCO,aAAa,EAAEa,WAAW,CAACpB,MAAM,GAAG,CAAC;YAAE;YACvCsD,IAAI,EAAE,wBAAwBO,IAAI,CAAC3B,IAAI,CAACC,WAAW,CAAC,CAAC,CAACiC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM;YAChFb,WAAW,EAAEc,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;YAAE;YAClCd,aAAa,EAAE,QAAQ;YACvBC,KAAK,EAAE,SAAS;YAAE;YAClBC,WAAW,EAAE,EAAE,GAAGW,IAAI,CAACE,KAAK,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UAClD,CAAC;QACH,CAAC,CAAC;QAEF1B,QAAQ,CAACe,cAAc,CAAC3D,MAAM,GAAG,CAAC,GAAG2D,cAAc,GAAGN,SAAS,CAAC;QAChEN,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOyB,GAAG,EAAE;QACZ/B,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEgC,GAAG,CAAC;QAClD5B,QAAQ,CAACS,SAAS,CAAC,CAAC,CAAC;QACrBN,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;MAClB;IACF,CAAC,MAAM,IAAI,CAACE,YAAY,IAAI,CAACE,YAAY,EAAE;MACzC;MACAP,QAAQ,CAACS,SAAS,CAAC;MACnBN,QAAQ,CAAC,IAAI,CAAC;IAChB;IAEAD,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,CAACpC,SAAS,EAAEC,SAAS,EAAEsC,YAAY,EAAEE,YAAY,EAAED,UAAU,EAAEE,UAAU,CAAC,CAAC;EAE9E,OAAO;IAAElC,KAAK;IAAE2B,OAAO;IAAEL;EAAM,CAAC;AAClC,CAAC;;AAED;AAAAG,GAAA,CAxEaD,iBAAiB;EAAA,QAK4CjD,oBAAoB,EACpBC,iCAAiC;AAAA;AAmE3G,OAAO,MAAM+E,eAAe,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACnC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiD,KAAK,EAAEO,QAAQ,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM;IAAEkB,IAAI,EAAEoE,UAAU;IAAE7B,SAAS,EAAE8B,aAAa;IAAEtC,KAAK,EAAEuC;EAAY,CAAC,GAAGpF,qBAAqB,CAAC,CAAC;EAClG,MAAM;IAAEc,IAAI,EAAEE,SAAS;IAAEqC,SAAS,EAAEG,YAAY;IAAEX,KAAK,EAAEY;EAAW,CAAC,GAAG1D,iCAAiC,CAAC,CAAC;EAE3GF,SAAS,CAAC,MAAM;IACd,IAAIsF,aAAa,IAAI3B,YAAY,EAAE;MACjCL,UAAU,CAAC,IAAI,CAAC;MAChB;IACF;IAEA,IAAIiC,WAAW,IAAI3B,UAAU,EAAE;MAC7BL,QAAQ,CAAC,2BAA2B,CAAC;MACrCD,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI+B,UAAU,IAAIlE,SAAS,EAAE;MAC3B,IAAI;QACF,MAAMqE,eAAe,GAAGH,UAAU,CAACjB,GAAG,CAACH,KAAK,IAAI;UAC9C;UACA,MAAMwB,UAAU,GAAGtE,SAAS,CAACU,MAAM,CAAC,MAAMgD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;;UAEhE,MAAMY,aAAa,GAAGD,UAAU,CAAC5D,MAAM,CAACC,IAAI;YAAA,IAAA6D,kBAAA,EAAAC,mBAAA,EAAAC,qBAAA;YAAA,QAAAF,kBAAA,GAC1C7D,IAAI,CAACW,YAAY,cAAAkD,kBAAA,wBAAAC,mBAAA,GAAjBD,kBAAA,CAAoB,CAAC,CAAC,cAAAC,mBAAA,wBAAAC,qBAAA,GAAtBD,mBAAA,CAAwBlD,IAAI,cAAAmD,qBAAA,uBAA5BA,qBAAA,CAA8BlD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,UAAU,CAAC;UAAA,CAClE,CAAC,CAACpC,MAAM,IAAIqE,IAAI,CAACE,KAAK,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;UAE/C,MAAMgB,cAAc,GAAGL,UAAU,CAAC5D,MAAM,CAACC,IAAI;YAAA,IAAAiE,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA;YAAA,QAAAF,mBAAA,GAC3CjE,IAAI,CAACW,YAAY,cAAAsD,mBAAA,wBAAAC,oBAAA,GAAjBD,mBAAA,CAAoB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAtBD,oBAAA,CAAwBtD,IAAI,cAAAuD,qBAAA,uBAA5BA,qBAAA,CAA8BtD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,WAAW,CAAC;UAAA,CACnE,CAAC,CAACpC,MAAM,IAAIqE,IAAI,CAACE,KAAK,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;UAE/C,MAAMoB,OAAO,GAAGT,UAAU,CAAC5D,MAAM,CAACC,IAAI;YAAA,IAAAqE,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA;YAAA,OACpC,EAAAL,mBAAA,GAAArE,IAAI,CAACW,YAAY,cAAA0D,mBAAA,wBAAAC,oBAAA,GAAjBD,mBAAA,CAAoB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAtBD,oBAAA,CAAwB1D,IAAI,cAAA2D,qBAAA,uBAA5BA,qBAAA,CAA8B1D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC,OAAA0D,mBAAA,GAC1DxE,IAAI,CAACW,YAAY,cAAA6D,mBAAA,wBAAAC,oBAAA,GAAjBD,mBAAA,CAAoB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAtBD,oBAAA,CAAwB7D,IAAI,cAAA8D,qBAAA,uBAA5BA,qBAAA,CAA8B7D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,CAAC;UAAA,CACjE,CAAC,CAACpC,MAAM,IAAIqE,IAAI,CAACE,KAAK,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;UAE9C,OAAO;YACLpC,IAAI,EAAEuB,KAAK,CAACwC,UAAU,IAAI,eAAe;YACzCC,QAAQ,EAAEhB,aAAa;YACvBiB,SAAS,EAAEb,cAAc;YACzBc,EAAE,EAAEV;UACN,CAAC;QACH,CAAC,CAAC;;QAEF;QACA,MAAMW,aAAa,GAAG,CACpB;UAAEnE,IAAI,EAAE,SAAS;UAAEgE,QAAQ,EAAE,EAAE;UAAEC,SAAS,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAC,EACvD;UAAElE,IAAI,EAAE,SAAS;UAAEgE,QAAQ,EAAE,EAAE;UAAEC,SAAS,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAC,EACvD;UAAElE,IAAI,EAAE,OAAO;UAAEgE,QAAQ,EAAE,EAAE;UAAEC,SAAS,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAC,CACtD;QAED,MAAME,WAAW,GAAGtB,eAAe,CAAChF,MAAM,GAAG,CAAC,GAAGgF,eAAe,GAAGqB,aAAa;;QAEhF;QACA,MAAME,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;QAC7CD,WAAW,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACpBH,KAAK,CAACI,OAAO,CAACF,CAAC,CAACvE,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,GAAGoE,KAAK,CAACI,OAAO,CAACD,CAAC,CAACxE,IAAI,CAACC,WAAW,CAAC,CAAC,CAC1E,CAAC;QAEDyC,SAAS,CAAC0B,WAAW,CAAC;QACtBvD,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOyB,GAAG,EAAE;QACZ/B,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEgC,GAAG,CAAC;QACnDzB,QAAQ,CAAC,6BAA6B,CAAC;MACzC;IACF;IAEAD,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,CAAC+B,UAAU,EAAElE,SAAS,EAAEmE,aAAa,EAAE3B,YAAY,EAAE4B,WAAW,EAAE3B,UAAU,CAAC,CAAC;EAEjF,OAAO;IAAEuB,MAAM;IAAE9B,OAAO;IAAEL;EAAM,CAAC;AACnC,CAAC;AAACkC,GAAA,CA1EWD,eAAe;EAAA,QAKiD9E,qBAAqB,EACxBD,iCAAiC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}