{"ast": null, "code": "import { baseApi } from './baseApi';\nimport { alertMessage } from '../../common/coreui';\nexport const dashboardApi = baseApi.injectEndpoints({\n  endpoints: builder => ({\n    // Get dashboard statistics\n    getDashboardStats: builder.query({\n      query: () => 'dashboard/stats',\n      providesTags: ['DashboardStats'],\n      async onQueryStarted(args, {\n        queryFulfilled\n      }) {\n        try {\n          await queryFulfilled;\n        } catch (error) {\n          console.warn('Dashboard stats endpoint not available, using computed data');\n        }\n      }\n    }),\n    // Get team statistics for dashboard\n    getTeamStats: builder.query({\n      query: () => 'dashboard/teams',\n      providesTags: ['TeamStats'],\n      async onQueryStarted(args, {\n        queryFulfilled\n      }) {\n        try {\n          await queryFulfilled;\n        } catch (error) {\n          console.warn('Team stats endpoint not available, using computed data');\n        }\n      }\n    }),\n    // Get shift statistics for dashboard\n    getShiftStats: builder.query({\n      query: () => 'dashboard/shifts',\n      providesTags: ['ShiftStats'],\n      async onQueryStarted(args, {\n        queryFulfilled\n      }) {\n        try {\n          await queryFulfilled;\n        } catch (error) {\n          console.warn('Shift stats endpoint not available, using computed data');\n        }\n      }\n    }),\n    // Get user dashboard data\n    getUserDashboard: builder.query({\n      query: () => 'dashboard/user',\n      providesTags: ['UserDashboard'],\n      async onQueryStarted(args, {\n        queryFulfilled\n      }) {\n        try {\n          await queryFulfilled;\n        } catch (error) {\n          console.warn('User dashboard endpoint not available, using computed data');\n        }\n      }\n    })\n  })\n});\nexport const {\n  useGetDashboardStatsQuery,\n  useGetTeamStatsQuery,\n  useGetShiftStatsQuery,\n  useGetUserDashboardQuery\n} = dashboardApi;", "map": {"version": 3, "names": ["baseApi", "alertMessage", "dashboardApi", "injectEndpoints", "endpoints", "builder", "getDashboardStats", "query", "providesTags", "onQueryStarted", "args", "queryFulfilled", "error", "console", "warn", "getTeamStats", "getShiftStats", "getUserDashboard", "useGetDashboardStatsQuery", "useGetTeamStatsQuery", "useGetShiftStatsQuery", "useGetUserDashboardQuery"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/features/api/dashboardApi.js"], "sourcesContent": ["import { baseApi } from './baseApi';\nimport { alertMessage } from '../../common/coreui';\n\nexport const dashboardApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    // Get dashboard statistics\n    getDashboardStats: builder.query({\n      query: () => 'dashboard/stats',\n      providesTags: ['DashboardStats'],\n      async onQueryStarted(args, { queryFulfilled }) {\n        try {\n          await queryFulfilled;\n        } catch (error) {\n          console.warn('Dashboard stats endpoint not available, using computed data');\n        }\n      },\n    }),\n\n    // Get team statistics for dashboard\n    getTeamStats: builder.query({\n      query: () => 'dashboard/teams',\n      providesTags: ['TeamStats'],\n      async onQueryStarted(args, { queryFulfilled }) {\n        try {\n          await queryFulfilled;\n        } catch (error) {\n          console.warn('Team stats endpoint not available, using computed data');\n        }\n      },\n    }),\n\n    // Get shift statistics for dashboard\n    getShiftStats: builder.query({\n      query: () => 'dashboard/shifts',\n      providesTags: ['ShiftStats'],\n      async onQueryStarted(args, { queryFulfilled }) {\n        try {\n          await queryFulfilled;\n        } catch (error) {\n          console.warn('Shift stats endpoint not available, using computed data');\n        }\n      },\n    }),\n\n    // Get user dashboard data\n    getUserDashboard: builder.query({\n      query: () => 'dashboard/user',\n      providesTags: ['UserDashboard'],\n      async onQueryStarted(args, { queryFulfilled }) {\n        try {\n          await queryFulfilled;\n        } catch (error) {\n          console.warn('User dashboard endpoint not available, using computed data');\n        }\n      },\n    }),\n  }),\n});\n\nexport const {\n  useGetDashboardStatsQuery,\n  useGetTeamStatsQuery,\n  useGetShiftStatsQuery,\n  useGetUserDashboardQuery,\n} = dashboardApi;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,SAASC,YAAY,QAAQ,qBAAqB;AAElD,OAAO,MAAMC,YAAY,GAAGF,OAAO,CAACG,eAAe,CAAC;EAClDC,SAAS,EAAGC,OAAO,KAAM;IACvB;IACAC,iBAAiB,EAAED,OAAO,CAACE,KAAK,CAAC;MAC/BA,KAAK,EAAEA,CAAA,KAAM,iBAAiB;MAC9BC,YAAY,EAAE,CAAC,gBAAgB,CAAC;MAChC,MAAMC,cAAcA,CAACC,IAAI,EAAE;QAAEC;MAAe,CAAC,EAAE;QAC7C,IAAI;UACF,MAAMA,cAAc;QACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACC,IAAI,CAAC,6DAA6D,CAAC;QAC7E;MACF;IACF,CAAC,CAAC;IAEF;IACAC,YAAY,EAAEV,OAAO,CAACE,KAAK,CAAC;MAC1BA,KAAK,EAAEA,CAAA,KAAM,iBAAiB;MAC9BC,YAAY,EAAE,CAAC,WAAW,CAAC;MAC3B,MAAMC,cAAcA,CAACC,IAAI,EAAE;QAAEC;MAAe,CAAC,EAAE;QAC7C,IAAI;UACF,MAAMA,cAAc;QACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACC,IAAI,CAAC,wDAAwD,CAAC;QACxE;MACF;IACF,CAAC,CAAC;IAEF;IACAE,aAAa,EAAEX,OAAO,CAACE,KAAK,CAAC;MAC3BA,KAAK,EAAEA,CAAA,KAAM,kBAAkB;MAC/BC,YAAY,EAAE,CAAC,YAAY,CAAC;MAC5B,MAAMC,cAAcA,CAACC,IAAI,EAAE;QAAEC;MAAe,CAAC,EAAE;QAC7C,IAAI;UACF,MAAMA,cAAc;QACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACC,IAAI,CAAC,yDAAyD,CAAC;QACzE;MACF;IACF,CAAC,CAAC;IAEF;IACAG,gBAAgB,EAAEZ,OAAO,CAACE,KAAK,CAAC;MAC9BA,KAAK,EAAEA,CAAA,KAAM,gBAAgB;MAC7BC,YAAY,EAAE,CAAC,eAAe,CAAC;MAC/B,MAAMC,cAAcA,CAACC,IAAI,EAAE;QAAEC;MAAe,CAAC,EAAE;QAC7C,IAAI;UACF,MAAMA,cAAc;QACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACC,IAAI,CAAC,4DAA4D,CAAC;QAC5E;MACF;IACF,CAAC;EACH,CAAC;AACH,CAAC,CAAC;AAEF,OAAO,MAAM;EACXI,yBAAyB;EACzBC,oBAAoB;EACpBC,qBAAqB;EACrBC;AACF,CAAC,GAAGnB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}