{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\dashboard\\\\ShiftSummarySection.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport Loading from \"../common/Loading\";\nimport { useShiftSummary } from \"../hooks/useTeamData\";\n\n// Remove the normalizeShift function as it's now handled in the hook\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SmallStat = ({\n  label,\n  value\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 px-4 py-3 flex items-center justify-between\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-sm font-medium text-gray-700 dark:text-gray-200\",\n    children: label\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center gap-2 text-gray-900 dark:text-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"\\uD83D\\uDC64\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"font-semibold\",\n      children: String(value).padStart(2, \"0\")\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 8,\n  columnNumber: 3\n}, this);\n_c = SmallStat;\nconst ShiftCard = ({\n  shift\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4\",\n  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n    className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3\",\n    children: [shift.name, \" Shift\"]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 sm:grid-cols-3 gap-3\",\n    children: [/*#__PURE__*/_jsxDEV(SmallStat, {\n      label: \"Total Designer\",\n      value: shift.designer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SmallStat, {\n      label: \"Total Developer\",\n      value: shift.developer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SmallStat, {\n      label: \"Total QA\",\n      value: shift.qa\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 18,\n  columnNumber: 3\n}, this);\n_c2 = ShiftCard;\nconst ShiftSummarySection = () => {\n  _s();\n  const {\n    shifts,\n    loading,\n    error\n  } = useShiftSummary();\n  if (loading) return /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-red-500 text-center py-4\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 21\n  }, this);\n  if (!shifts.length) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-gray-500 text-center py-4\",\n    children: \"No shift data available\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 30\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 lg:grid-cols-3 gap-4\",\n    children: shifts.map((shift, i) => /*#__PURE__*/_jsxDEV(ShiftCard, {\n      shift: shift\n    }, `${shift.name}-${i}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(ShiftSummarySection, \"XgA6ceOv5Uj2UOeq9OtbeTh2AKs=\", false, function () {\n  return [useShiftSummary];\n});\n_c3 = ShiftSummarySection;\nexport default ShiftSummarySection;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"SmallStat\");\n$RefreshReg$(_c2, \"ShiftCard\");\n$RefreshReg$(_c3, \"ShiftSummarySection\");", "map": {"version": 3, "names": ["React", "Loading", "useShiftSummary", "jsxDEV", "_jsxDEV", "SmallStat", "label", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "String", "padStart", "_c", "ShiftCard", "shift", "name", "designer", "developer", "qa", "_c2", "ShiftSummarySection", "_s", "shifts", "loading", "error", "length", "map", "i", "_c3", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/dashboard/ShiftSummarySection.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport Loading from \"../common/Loading\";\r\nimport { useShiftSummary } from \"../hooks/useTeamData\";\r\n\r\n// Remove the normalizeShift function as it's now handled in the hook\r\n\r\nconst SmallStat = ({ label, value }) => (\r\n  <div className=\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 px-4 py-3 flex items-center justify-between\">\r\n    <div className=\"text-sm font-medium text-gray-700 dark:text-gray-200\">{label}</div>\r\n    <div className=\"flex items-center gap-2 text-gray-900 dark:text-gray-100\">\r\n      <span>👤</span>\r\n      <span className=\"font-semibold\">{String(value).padStart(2, \"0\")}</span>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst ShiftCard = ({ shift }) => (\r\n  <div className=\"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4\">\r\n    <h4 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3\">{shift.name} Shift</h4>\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-3\">\r\n      <SmallStat label=\"Total Designer\" value={shift.designer} />\r\n      <SmallStat label=\"Total Developer\" value={shift.developer} />\r\n      <SmallStat label=\"Total QA\" value={shift.qa} />\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst ShiftSummarySection = () => {\r\n  const { shifts, loading, error } = useShiftSummary();\r\n\r\n  if (loading) return <Loading />;\r\n  if (error) return <div className=\"text-red-500 text-center py-4\">{error}</div>;\r\n  if (!shifts.length) return <div className=\"text-gray-500 text-center py-4\">No shift data available</div>;\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\r\n      {shifts.map((shift, i) => (\r\n        <ShiftCard key={`${shift.name}-${i}`} shift={shift} />\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ShiftSummarySection;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,mBAAmB;AACvC,SAASC,eAAe,QAAQ,sBAAsB;;AAEtD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,SAAS,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAM,CAAC,kBACjCH,OAAA;EAAKI,SAAS,EAAC,mIAAmI;EAAAC,QAAA,gBAChJL,OAAA;IAAKI,SAAS,EAAC,sDAAsD;IAAAC,QAAA,EAAEH;EAAK;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC,eACnFT,OAAA;IAAKI,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBACvEL,OAAA;MAAAK,QAAA,EAAM;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACfT,OAAA;MAAMI,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEK,MAAM,CAACP,KAAK,CAAC,CAACQ,QAAQ,CAAC,CAAC,EAAE,GAAG;IAAC;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpE,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACG,EAAA,GARIX,SAAS;AAUf,MAAMY,SAAS,GAAGA,CAAC;EAAEC;AAAM,CAAC,kBAC1Bd,OAAA;EAAKI,SAAS,EAAC,uFAAuF;EAAAC,QAAA,gBACpGL,OAAA;IAAII,SAAS,EAAC,6DAA6D;IAAAC,QAAA,GAAES,KAAK,CAACC,IAAI,EAAC,QAAM;EAAA;IAAAT,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eACnGT,OAAA;IAAKI,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBACpDL,OAAA,CAACC,SAAS;MAACC,KAAK,EAAC,gBAAgB;MAACC,KAAK,EAAEW,KAAK,CAACE;IAAS;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC3DT,OAAA,CAACC,SAAS;MAACC,KAAK,EAAC,iBAAiB;MAACC,KAAK,EAAEW,KAAK,CAACG;IAAU;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7DT,OAAA,CAACC,SAAS;MAACC,KAAK,EAAC,UAAU;MAACC,KAAK,EAAEW,KAAK,CAACI;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5C,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACU,GAAA,GATIN,SAAS;AAWf,MAAMO,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAG1B,eAAe,CAAC,CAAC;EAEpD,IAAIyB,OAAO,EAAE,oBAAOvB,OAAA,CAACH,OAAO;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC/B,IAAIe,KAAK,EAAE,oBAAOxB,OAAA;IAAKI,SAAS,EAAC,+BAA+B;IAAAC,QAAA,EAAEmB;EAAK;IAAAlB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC9E,IAAI,CAACa,MAAM,CAACG,MAAM,EAAE,oBAAOzB,OAAA;IAAKI,SAAS,EAAC,gCAAgC;IAAAC,QAAA,EAAC;EAAuB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAExG,oBACET,OAAA;IAAKI,SAAS,EAAC,uCAAuC;IAAAC,QAAA,EACnDiB,MAAM,CAACI,GAAG,CAAC,CAACZ,KAAK,EAAEa,CAAC,kBACnB3B,OAAA,CAACa,SAAS;MAA4BC,KAAK,EAAEA;IAAM,GAAnC,GAAGA,KAAK,CAACC,IAAI,IAAIY,CAAC,EAAE;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAiB,CACtD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACY,EAAA,CAdID,mBAAmB;EAAA,QACYtB,eAAe;AAAA;AAAA8B,GAAA,GAD9CR,mBAAmB;AAgBzB,eAAeA,mBAAmB;AAAC,IAAAR,EAAA,EAAAO,GAAA,EAAAS,GAAA;AAAAC,YAAA,CAAAjB,EAAA;AAAAiB,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}