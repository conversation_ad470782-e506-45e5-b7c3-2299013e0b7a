{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\dashboard\\\\ClientTeamsSection.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport Loading from \"../common/Loading\";\nimport { useDashboardTeams } from \"../hooks/useTeamData\";\n\n// Component to display a single team card\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TeamCard = ({\n  team\n}) => {\n  var _team$name, _team$name$;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 truncate\",\n          children: team.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2 mt-1\",\n          children: [team.isTopClient && /*#__PURE__*/_jsxDEV(\"span\", {\n            title: \"Top Client\",\n            className: \"text-amber-500 text-sm\",\n            children: \"\\uD83D\\uDD06 Priority Client\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 32\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500\",\n            children: \"|\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600\",\n            children: team.shift\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: team.logo ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: team.logo,\n          alt: `${team.name} logo`,\n          className: \"w-12 h-12 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-lg font-medium text-gray-500\",\n          children: ((_team$name = team.name) === null || _team$name === void 0 ? void 0 : (_team$name$ = _team$name[0]) === null || _team$name$ === void 0 ? void 0 : _team$name$.toUpperCase()) || \"T\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined text-gray-400\",\n          children: \"person\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Lead:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-gray-900 dark:text-gray-100\",\n          children: team.teamLead\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined text-gray-400\",\n          children: \"payments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Status:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-gray-900 dark:text-gray-100\",\n          children: team.billingStatus\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 grid grid-cols-2 gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600 dark:text-gray-300\",\n          children: \"Total Members\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDC65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold\",\n            children: team.totalMembers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600 dark:text-gray-300\",\n          children: \"Billable Hours\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u23F1\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold\",\n            children: team.billableHours\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 5\n    }, this), team.billingRate > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-3 text-xs text-right text-gray-500\",\n      children: [\"Rate: $\", team.billingRate, \"/hr\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 3\n  }, this);\n};\n\n// Function to normalize team data\n_c = TeamCard;\nconst normalizeTeam = (item, idx) => {\n  var _ref, _ref2, _item$id, _item$team_lead, _ref3, _ref4, _item$total_members, _ref5, _ref6, _item$billable_hours, _ref7, _ref8, _item$is_top_client;\n  const name = (item === null || item === void 0 ? void 0 : item.name) || (item === null || item === void 0 ? void 0 : item.team_name) || (item === null || item === void 0 ? void 0 : item.client_name) || `Team ${idx + 1}`;\n  return {\n    id: (_ref = (_ref2 = (_item$id = item === null || item === void 0 ? void 0 : item.id) !== null && _item$id !== void 0 ? _item$id : item === null || item === void 0 ? void 0 : item.team_id) !== null && _ref2 !== void 0 ? _ref2 : item === null || item === void 0 ? void 0 : item.client_id) !== null && _ref !== void 0 ? _ref : `${name}-${idx}`,\n    name,\n    teamLead: (item === null || item === void 0 ? void 0 : (_item$team_lead = item.team_lead) === null || _item$team_lead === void 0 ? void 0 : _item$team_lead.name) || (item === null || item === void 0 ? void 0 : item.team_lead) || (item === null || item === void 0 ? void 0 : item.lead_name) || \"—\",\n    totalMembers: String((_ref3 = (_ref4 = (_item$total_members = item === null || item === void 0 ? void 0 : item.total_members) !== null && _item$total_members !== void 0 ? _item$total_members : item === null || item === void 0 ? void 0 : item.members_count) !== null && _ref4 !== void 0 ? _ref4 : item === null || item === void 0 ? void 0 : item.member_count) !== null && _ref3 !== void 0 ? _ref3 : 0).padStart(2, '0'),\n    billableHours: `${(_ref5 = (_ref6 = (_item$billable_hours = item === null || item === void 0 ? void 0 : item.billable_hours) !== null && _item$billable_hours !== void 0 ? _item$billable_hours : item === null || item === void 0 ? void 0 : item.hours) !== null && _ref6 !== void 0 ? _ref6 : item === null || item === void 0 ? void 0 : item.hour) !== null && _ref5 !== void 0 ? _ref5 : 0}hr`,\n    logo: (item === null || item === void 0 ? void 0 : item.logo_url) || (item === null || item === void 0 ? void 0 : item.icon) || (item === null || item === void 0 ? void 0 : item.team_logo) || (item === null || item === void 0 ? void 0 : item.client_logo) || `/assets/client-logos/${name.toLowerCase().replace(/\\s+/g, '-')}.png`,\n    isTopClient: Boolean((_ref7 = (_ref8 = (_item$is_top_client = item === null || item === void 0 ? void 0 : item.is_top_client) !== null && _item$is_top_client !== void 0 ? _item$is_top_client : item === null || item === void 0 ? void 0 : item.top_client) !== null && _ref8 !== void 0 ? _ref8 : item === null || item === void 0 ? void 0 : item.priority_client) !== null && _ref7 !== void 0 ? _ref7 : false),\n    billingStatus: (item === null || item === void 0 ? void 0 : item.billing_status) || \"Not Set\",\n    shift: (item === null || item === void 0 ? void 0 : item.shift) || \"Not Assigned\",\n    billingRate: (item === null || item === void 0 ? void 0 : item.billing_rate) || 0\n  };\n};\n\n// Main component\nfunction ClientTeamsSection() {\n  _s();\n  const [teams, setTeams] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [err, setErr] = useState(null);\n  useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    if (!token) {\n      setErr(\"No auth token found\");\n      setLoading(false);\n      return;\n    }\n    const load = async () => {\n      setLoading(true);\n      try {\n        const mockTeams = [{\n          name: \"AccuWeather\",\n          team_lead: \"Kamal Hossain\",\n          total_members: 10,\n          billable_hours: 80,\n          logo: \"/assets/client-logos/accuweather.png\",\n          is_top_client: true\n        }, {\n          name: \"Bloomberg\",\n          team_lead: \"Kamal Hossain\",\n          total_members: 8,\n          billable_hours: 64,\n          logo: \"/assets/client-logos/bloomberg.png\",\n          is_top_client: true\n        }, {\n          name: \"Boats Group\",\n          team_lead: \"Aminul Islam\",\n          total_members: 13,\n          billable_hours: 104,\n          logo: \"/assets/client-logos/boats-group.png\",\n          is_top_client: true\n        }, {\n          name: \"Clipcentric\",\n          team_lead: \"Aminul Islam\",\n          total_members: 15,\n          billable_hours: 120,\n          logo: \"/assets/client-logos/clipcentric.png\"\n        }, {\n          name: \"MultiView\",\n          team_lead: \"Hasan Ahmed\",\n          total_members: 5,\n          billable_hours: 40,\n          logo: \"/assets/client-logos/multiview.png\"\n        }, {\n          name: \"Bigtincan\",\n          team_lead: \"Nafiul Islam\",\n          total_members: 5,\n          billable_hours: 40,\n          logo: \"/assets/client-logos/bigtincan.png\"\n        }];\n        let finalTeams = mockTeams;\n        try {\n          const response = await fetch(`${API_URL}/teams`, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json',\n              'Accept': 'application/json'\n            }\n          });\n          if (response.ok) {\n            var _data$data;\n            const data = await response.json();\n            if ((data === null || data === void 0 ? void 0 : (_data$data = data.data) === null || _data$data === void 0 ? void 0 : _data$data.length) > 0 || Array.isArray(data) && data.length > 0) {\n              finalTeams = (data === null || data === void 0 ? void 0 : data.data) || data;\n            }\n          }\n        } catch (error) {\n          console.warn('Failed to fetch from API, using mock data:', error);\n        }\n        setTeams(finalTeams.map((item, idx) => normalizeTeam(item, idx)));\n      } catch (error) {\n        console.error(\"Error fetching teams:\", error);\n        setErr(\"Unable to load teams. Please try again later.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    load();\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 23\n  }, this);\n  if (err) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-red-500\",\n    children: err\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 186,\n    columnNumber: 19\n  }, this);\n  if (!teams.length) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4\",\n    children: teams.map(team => /*#__PURE__*/_jsxDEV(TeamCard, {\n      team: team\n    }, team.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 5\n  }, this);\n}\n\n// Export the component\n_s(ClientTeamsSection, \"91r8IFH4UttcDHrYvjpyXegUIh4=\");\n_c2 = ClientTeamsSection;\nexport default ClientTeamsSection;\nvar _c, _c2;\n$RefreshReg$(_c, \"TeamCard\");\n$RefreshReg$(_c2, \"ClientTeamsSection\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Loading", "useDashboardTeams", "jsxDEV", "_jsxDEV", "TeamCard", "team", "_team$name", "_team$name$", "className", "children", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isTopClient", "title", "shift", "logo", "src", "alt", "toUpperCase", "teamLead", "billingStatus", "totalMembers", "billableHours", "billingRate", "_c", "normalizeTeam", "item", "idx", "_ref", "_ref2", "_item$id", "_item$team_lead", "_ref3", "_ref4", "_item$total_members", "_ref5", "_ref6", "_item$billable_hours", "_ref7", "_ref8", "_item$is_top_client", "team_name", "client_name", "id", "team_id", "client_id", "team_lead", "lead_name", "String", "total_members", "members_count", "member_count", "padStart", "billable_hours", "hours", "hour", "logo_url", "icon", "team_logo", "client_logo", "toLowerCase", "replace", "Boolean", "is_top_client", "top_client", "priority_client", "billing_status", "billing_rate", "ClientTeamsSection", "_s", "teams", "setTeams", "loading", "setLoading", "err", "setErr", "token", "localStorage", "getItem", "load", "mockTeams", "finalTeams", "response", "fetch", "API_URL", "headers", "ok", "_data$data", "data", "json", "length", "Array", "isArray", "error", "console", "warn", "map", "_c2", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/dashboard/ClientTeamsSection.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport Loading from \"../common/Loading\";\r\nimport { useDashboardTeams } from \"../hooks/useTeamData\";\r\n\r\n// Component to display a single team card\r\nconst TeamCard = ({ team }) => (\r\n  <div className=\"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow p-4\">\r\n    <div className=\"flex items-center justify-between\">\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 truncate\">{team.name}</h3>\r\n        <div className=\"flex items-center gap-2 mt-1\">\r\n          {team.isTopClient && <span title=\"Top Client\" className=\"text-amber-500 text-sm\">🔆 Priority Client</span>}\r\n          <span className=\"text-xs text-gray-500\">|</span>\r\n          <span className=\"text-sm text-gray-600\">{team.shift}</span>\r\n        </div>\r\n      </div>\r\n      <div className=\"flex items-center gap-2\">\r\n        {team.logo ? (\r\n          <img\r\n            src={team.logo}\r\n            alt={`${team.name} logo`}\r\n            className=\"w-12 h-12 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-600\"\r\n          />\r\n        ) : (\r\n          <div className=\"w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-lg font-medium text-gray-500\">\r\n            {team.name?.[0]?.toUpperCase() || \"T\"}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n\r\n    <div className=\"mt-4 space-y-2\">\r\n      <p className=\"text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2\">\r\n        <span className=\"material-symbols-outlined text-gray-400\">person</span>\r\n        <span>Lead:</span>\r\n        <span className=\"font-medium text-gray-900 dark:text-gray-100\">{team.teamLead}</span>\r\n      </p>\r\n      \r\n      <p className=\"text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2\">\r\n        <span className=\"material-symbols-outlined text-gray-400\">payments</span>\r\n        <span>Status:</span>\r\n        <span className=\"font-medium text-gray-900 dark:text-gray-100\">{team.billingStatus}</span>\r\n      </p>\r\n    </div>\r\n\r\n    <div className=\"mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 grid grid-cols-2 gap-3\">\r\n      <div className=\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\">\r\n        <p className=\"text-xs text-gray-600 dark:text-gray-300\">Total Members</p>\r\n        <div className=\"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\">\r\n          <span>👥</span>\r\n          <span className=\"font-semibold\">{team.totalMembers}</span>\r\n        </div>\r\n      </div>\r\n      <div className=\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\">\r\n        <p className=\"text-xs text-gray-600 dark:text-gray-300\">Billable Hours</p>\r\n        <div className=\"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\">\r\n          <span>⏱️</span>\r\n          <span className=\"font-semibold\">{team.billableHours}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    {team.billingRate > 0 && (\r\n      <div className=\"mt-3 text-xs text-right text-gray-500\">\r\n        Rate: ${team.billingRate}/hr\r\n      </div>\r\n    )}\r\n  </div>\r\n);\r\n\r\n// Function to normalize team data\r\nconst normalizeTeam = (item, idx) => {\r\n  const name = item?.name || item?.team_name || item?.client_name || `Team ${idx + 1}`;\r\n  return {\r\n    id: item?.id ?? item?.team_id ?? item?.client_id ?? `${name}-${idx}`,\r\n    name,\r\n    teamLead: item?.team_lead?.name || item?.team_lead || item?.lead_name || \"—\",\r\n    totalMembers: String(item?.total_members ?? item?.members_count ?? item?.member_count ?? 0).padStart(2, '0'),\r\n    billableHours: `${item?.billable_hours ?? item?.hours ?? item?.hour ?? 0}hr`,\r\n    logo: item?.logo_url || item?.icon || item?.team_logo || item?.client_logo || `/assets/client-logos/${name.toLowerCase().replace(/\\s+/g, '-')}.png`,\r\n    isTopClient: Boolean(item?.is_top_client ?? item?.top_client ?? item?.priority_client ?? false),\r\n    billingStatus: item?.billing_status || \"Not Set\",\r\n    shift: item?.shift || \"Not Assigned\",\r\n    billingRate: item?.billing_rate || 0,\r\n  };\r\n};\r\n\r\n// Main component\r\nfunction ClientTeamsSection() {\r\n  const [teams, setTeams] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [err, setErr] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (!token) {\r\n      setErr(\"No auth token found\");\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    const load = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const mockTeams = [\r\n          {\r\n            name: \"AccuWeather\",\r\n            team_lead: \"Kamal Hossain\",\r\n            total_members: 10,\r\n            billable_hours: 80,\r\n            logo: \"/assets/client-logos/accuweather.png\",\r\n            is_top_client: true\r\n          },\r\n          {\r\n            name: \"Bloomberg\",\r\n            team_lead: \"Kamal Hossain\",\r\n            total_members: 8,\r\n            billable_hours: 64,\r\n            logo: \"/assets/client-logos/bloomberg.png\",\r\n            is_top_client: true\r\n          },\r\n          {\r\n            name: \"Boats Group\",\r\n            team_lead: \"Aminul Islam\",\r\n            total_members: 13,\r\n            billable_hours: 104,\r\n            logo: \"/assets/client-logos/boats-group.png\",\r\n            is_top_client: true\r\n          },\r\n          {\r\n            name: \"Clipcentric\",\r\n            team_lead: \"Aminul Islam\",\r\n            total_members: 15,\r\n            billable_hours: 120,\r\n            logo: \"/assets/client-logos/clipcentric.png\"\r\n          },\r\n          {\r\n            name: \"MultiView\",\r\n            team_lead: \"Hasan Ahmed\",\r\n            total_members: 5,\r\n            billable_hours: 40,\r\n            logo: \"/assets/client-logos/multiview.png\"\r\n          },\r\n          {\r\n            name: \"Bigtincan\",\r\n            team_lead: \"Nafiul Islam\",\r\n            total_members: 5,\r\n            billable_hours: 40,\r\n            logo: \"/assets/client-logos/bigtincan.png\"\r\n          }\r\n        ];\r\n\r\n        let finalTeams = mockTeams;\r\n\r\n        try {\r\n          const response = await fetch(`${API_URL}/teams`, {\r\n            headers: { \r\n              'Authorization': `Bearer ${token}`,\r\n              'Content-Type': 'application/json',\r\n              'Accept': 'application/json'\r\n            },\r\n          });\r\n\r\n          if (response.ok) {\r\n            const data = await response.json();\r\n            if (data?.data?.length > 0 || (Array.isArray(data) && data.length > 0)) {\r\n              finalTeams = data?.data || data;\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.warn('Failed to fetch from API, using mock data:', error);\r\n        }\r\n\r\n        setTeams(finalTeams.map((item, idx) => normalizeTeam(item, idx)));\r\n      } catch (error) {\r\n        console.error(\"Error fetching teams:\", error);\r\n        setErr(\"Unable to load teams. Please try again later.\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    load();\r\n  }, []);\r\n\r\n  if (loading) return <Loading />;\r\n  if (err) return <div className=\"text-red-500\">{err}</div>;\r\n  if (!teams.length) return null;\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4\">\r\n      {teams.map((team) => (\r\n        <TeamCard key={team.id} team={team} />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\n// Export the component\r\nexport default ClientTeamsSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,OAAO,MAAM,mBAAmB;AACvC,SAASC,iBAAiB,QAAQ,sBAAsB;;AAExD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC;EAAA,IAAAC,UAAA,EAAAC,WAAA;EAAA,oBACxBJ,OAAA;IAAKK,SAAS,EAAC,mIAAmI;IAAAC,QAAA,gBAChJN,OAAA;MAAKK,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDN,OAAA;QAAAM,QAAA,gBACEN,OAAA;UAAIK,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAAEJ,IAAI,CAACK;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChGX,OAAA;UAAKK,SAAS,EAAC,8BAA8B;UAAAC,QAAA,GAC1CJ,IAAI,CAACU,WAAW,iBAAIZ,OAAA;YAAMa,KAAK,EAAC,YAAY;YAACR,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1GX,OAAA;YAAMK,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDX,OAAA;YAAMK,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAEJ,IAAI,CAACY;UAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNX,OAAA;QAAKK,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EACrCJ,IAAI,CAACa,IAAI,gBACRf,OAAA;UACEgB,GAAG,EAAEd,IAAI,CAACa,IAAK;UACfE,GAAG,EAAE,GAAGf,IAAI,CAACK,IAAI,OAAQ;UACzBF,SAAS,EAAC;QAA6E;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,gBAEFX,OAAA;UAAKK,SAAS,EAAC,wHAAwH;UAAAC,QAAA,EACpI,EAAAH,UAAA,GAAAD,IAAI,CAACK,IAAI,cAAAJ,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAgBc,WAAW,CAAC,CAAC,KAAI;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENX,OAAA;MAAKK,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BN,OAAA;QAAGK,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAC7EN,OAAA;UAAMK,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvEX,OAAA;UAAAM,QAAA,EAAM;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBX,OAAA;UAAMK,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAEJ,IAAI,CAACiB;QAAQ;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC,eAEJX,OAAA;QAAGK,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAC7EN,OAAA;UAAMK,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzEX,OAAA;UAAAM,QAAA,EAAM;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpBX,OAAA;UAAMK,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAEJ,IAAI,CAACkB;QAAa;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENX,OAAA;MAAKK,SAAS,EAAC,gFAAgF;MAAAC,QAAA,gBAC7FN,OAAA;QAAKK,SAAS,EAAC,2FAA2F;QAAAC,QAAA,gBACxGN,OAAA;UAAGK,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzEX,OAAA;UAAKK,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAC5EN,OAAA;YAAAM,QAAA,EAAM;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfX,OAAA;YAAMK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEJ,IAAI,CAACmB;UAAY;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNX,OAAA;QAAKK,SAAS,EAAC,2FAA2F;QAAAC,QAAA,gBACxGN,OAAA;UAAGK,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC1EX,OAAA;UAAKK,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAC5EN,OAAA;YAAAM,QAAA,EAAM;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfX,OAAA;YAAMK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEJ,IAAI,CAACoB;UAAa;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELT,IAAI,CAACqB,WAAW,GAAG,CAAC,iBACnBvB,OAAA;MAAKK,SAAS,EAAC,uCAAuC;MAAAC,QAAA,GAAC,SAC9C,EAACJ,IAAI,CAACqB,WAAW,EAAC,KAC3B;IAAA;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAAA,CACP;;AAED;AAAAa,EAAA,GAjEMvB,QAAQ;AAkEd,MAAMwB,aAAa,GAAGA,CAACC,IAAI,EAAEC,GAAG,KAAK;EAAA,IAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA,EAAAC,eAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,mBAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,oBAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,mBAAA;EACnC,MAAMjC,IAAI,GAAG,CAAAmB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEnB,IAAI,MAAImB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,SAAS,MAAIf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,WAAW,KAAI,QAAQf,GAAG,GAAG,CAAC,EAAE;EACpF,OAAO;IACLgB,EAAE,GAAAf,IAAA,IAAAC,KAAA,IAAAC,QAAA,GAAEJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,EAAE,cAAAb,QAAA,cAAAA,QAAA,GAAIJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,OAAO,cAAAf,KAAA,cAAAA,KAAA,GAAIH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,SAAS,cAAAjB,IAAA,cAAAA,IAAA,GAAI,GAAGrB,IAAI,IAAIoB,GAAG,EAAE;IACpEpB,IAAI;IACJY,QAAQ,EAAE,CAAAO,IAAI,aAAJA,IAAI,wBAAAK,eAAA,GAAJL,IAAI,CAAEoB,SAAS,cAAAf,eAAA,uBAAfA,eAAA,CAAiBxB,IAAI,MAAImB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,SAAS,MAAIpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,SAAS,KAAI,GAAG;IAC5E1B,YAAY,EAAE2B,MAAM,EAAAhB,KAAA,IAAAC,KAAA,IAAAC,mBAAA,GAACR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuB,aAAa,cAAAf,mBAAA,cAAAA,mBAAA,GAAIR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,aAAa,cAAAjB,KAAA,cAAAA,KAAA,GAAIP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,YAAY,cAAAnB,KAAA,cAAAA,KAAA,GAAI,CAAC,CAAC,CAACoB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC5G9B,aAAa,EAAE,IAAAa,KAAA,IAAAC,KAAA,IAAAC,oBAAA,GAAGX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,cAAc,cAAAhB,oBAAA,cAAAA,oBAAA,GAAIX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,KAAK,cAAAlB,KAAA,cAAAA,KAAA,GAAIV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,IAAI,cAAApB,KAAA,cAAAA,KAAA,GAAI,CAAC,IAAI;IAC5EpB,IAAI,EAAE,CAAAW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,QAAQ,MAAI9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,IAAI,MAAI/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,SAAS,MAAIhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,WAAW,KAAI,wBAAwBpD,IAAI,CAACqD,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM;IACnJjD,WAAW,EAAEkD,OAAO,EAAAxB,KAAA,IAAAC,KAAA,IAAAC,mBAAA,GAACd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqC,aAAa,cAAAvB,mBAAA,cAAAA,mBAAA,GAAId,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC,UAAU,cAAAzB,KAAA,cAAAA,KAAA,GAAIb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuC,eAAe,cAAA3B,KAAA,cAAAA,KAAA,GAAI,KAAK,CAAC;IAC/FlB,aAAa,EAAE,CAAAM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC,cAAc,KAAI,SAAS;IAChDpD,KAAK,EAAE,CAAAY,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEZ,KAAK,KAAI,cAAc;IACpCS,WAAW,EAAE,CAAAG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,YAAY,KAAI;EACrC,CAAC;AACH,CAAC;;AAED;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6E,OAAO,EAAEC,UAAU,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+E,GAAG,EAAEC,MAAM,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;EAEpCD,SAAS,CAAC,MAAM;IACd,MAAMkF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVD,MAAM,CAAC,qBAAqB,CAAC;MAC7BF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,MAAMM,IAAI,GAAG,MAAAA,CAAA,KAAY;MACvBN,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMO,SAAS,GAAG,CAChB;UACEzE,IAAI,EAAE,aAAa;UACnBuC,SAAS,EAAE,eAAe;UAC1BG,aAAa,EAAE,EAAE;UACjBI,cAAc,EAAE,EAAE;UAClBtC,IAAI,EAAE,sCAAsC;UAC5CgD,aAAa,EAAE;QACjB,CAAC,EACD;UACExD,IAAI,EAAE,WAAW;UACjBuC,SAAS,EAAE,eAAe;UAC1BG,aAAa,EAAE,CAAC;UAChBI,cAAc,EAAE,EAAE;UAClBtC,IAAI,EAAE,oCAAoC;UAC1CgD,aAAa,EAAE;QACjB,CAAC,EACD;UACExD,IAAI,EAAE,aAAa;UACnBuC,SAAS,EAAE,cAAc;UACzBG,aAAa,EAAE,EAAE;UACjBI,cAAc,EAAE,GAAG;UACnBtC,IAAI,EAAE,sCAAsC;UAC5CgD,aAAa,EAAE;QACjB,CAAC,EACD;UACExD,IAAI,EAAE,aAAa;UACnBuC,SAAS,EAAE,cAAc;UACzBG,aAAa,EAAE,EAAE;UACjBI,cAAc,EAAE,GAAG;UACnBtC,IAAI,EAAE;QACR,CAAC,EACD;UACER,IAAI,EAAE,WAAW;UACjBuC,SAAS,EAAE,aAAa;UACxBG,aAAa,EAAE,CAAC;UAChBI,cAAc,EAAE,EAAE;UAClBtC,IAAI,EAAE;QACR,CAAC,EACD;UACER,IAAI,EAAE,WAAW;UACjBuC,SAAS,EAAE,cAAc;UACzBG,aAAa,EAAE,CAAC;UAChBI,cAAc,EAAE,EAAE;UAClBtC,IAAI,EAAE;QACR,CAAC,CACF;QAED,IAAIkE,UAAU,GAAGD,SAAS;QAE1B,IAAI;UACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,QAAQ,EAAE;YAC/CC,OAAO,EAAE;cACP,eAAe,EAAE,UAAUT,KAAK,EAAE;cAClC,cAAc,EAAE,kBAAkB;cAClC,QAAQ,EAAE;YACZ;UACF,CAAC,CAAC;UAEF,IAAIM,QAAQ,CAACI,EAAE,EAAE;YAAA,IAAAC,UAAA;YACf,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;YAClC,IAAI,CAAAD,IAAI,aAAJA,IAAI,wBAAAD,UAAA,GAAJC,IAAI,CAAEA,IAAI,cAAAD,UAAA,uBAAVA,UAAA,CAAYG,MAAM,IAAG,CAAC,IAAKC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,IAAIA,IAAI,CAACE,MAAM,GAAG,CAAE,EAAE;cACtET,UAAU,GAAG,CAAAO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEA,IAAI,KAAIA,IAAI;YACjC;UACF;QACF,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdC,OAAO,CAACC,IAAI,CAAC,4CAA4C,EAAEF,KAAK,CAAC;QACnE;QAEAtB,QAAQ,CAACU,UAAU,CAACe,GAAG,CAAC,CAACtE,IAAI,EAAEC,GAAG,KAAKF,aAAa,CAACC,IAAI,EAAEC,GAAG,CAAC,CAAC,CAAC;MACnE,CAAC,CAAC,OAAOkE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7ClB,MAAM,CAAC,+CAA+C,CAAC;MACzD,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDM,IAAI,CAAC,CAAC;EACR,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIP,OAAO,EAAE,oBAAOxE,OAAA,CAACH,OAAO;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC/B,IAAI+D,GAAG,EAAE,oBAAO1E,OAAA;IAAKK,SAAS,EAAC,cAAc;IAAAC,QAAA,EAAEoE;EAAG;IAAAlE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACzD,IAAI,CAAC2D,KAAK,CAACoB,MAAM,EAAE,OAAO,IAAI;EAE9B,oBACE1F,OAAA;IAAKK,SAAS,EAAC,qFAAqF;IAAAC,QAAA,EACjGgE,KAAK,CAAC0B,GAAG,CAAE9F,IAAI,iBACdF,OAAA,CAACC,QAAQ;MAAeC,IAAI,EAAEA;IAAK,GAApBA,IAAI,CAACyC,EAAE;MAAAnC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAe,CACtC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;;AAEA;AAAA0D,EAAA,CA7GSD,kBAAkB;AAAA6B,GAAA,GAAlB7B,kBAAkB;AA8G3B,eAAeA,kBAAkB;AAAC,IAAA5C,EAAA,EAAAyE,GAAA;AAAAC,YAAA,CAAA1E,EAAA;AAAA0E,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}