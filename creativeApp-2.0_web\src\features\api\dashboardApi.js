import { baseApi } from './baseApi';
import { alertMessage } from '../../common/coreui';

export const dashboardApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get dashboard statistics
    getDashboardStats: builder.query({
      query: () => 'dashboard/stats',
      providesTags: ['DashboardStats'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          console.warn('Dashboard stats endpoint not available, using computed data');
        }
      },
    }),

    // Get team statistics for dashboard
    getTeamStats: builder.query({
      query: () => 'dashboard/teams',
      providesTags: ['TeamStats'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          console.warn('Team stats endpoint not available, using computed data');
        }
      },
    }),

    // Get shift statistics for dashboard
    getShiftStats: builder.query({
      query: () => 'dashboard/shifts',
      providesTags: ['ShiftStats'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          console.warn('Shift stats endpoint not available, using computed data');
        }
      },
    }),

    // Get user dashboard data
    getUserDashboard: builder.query({
      query: () => 'dashboard/user',
      providesTags: ['UserDashboard'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          console.warn('User dashboard endpoint not available, using computed data');
        }
      },
    }),
  }),
});

export const {
  useGetDashboardStatsQuery,
  useGetTeamStatsQuery,
  useGetShiftStatsQuery,
  useGetUserDashboardQuery,
} = dashboardApi;
