{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\dashboard\\\\ClientTeamsSection.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport Loading from \"../common/Loading\";\nimport { useDashboardTeams } from \"../hooks/useTeamData\";\n\n// Component to display a single team card\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TeamCard = ({\n  team\n}) => {\n  var _team$name, _team$name$;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 truncate\",\n          children: team.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2 mt-1\",\n          children: [team.isTopClient && /*#__PURE__*/_jsxDEV(\"span\", {\n            title: \"Top Client\",\n            className: \"text-amber-500 text-sm\",\n            children: \"\\uD83D\\uDD06 Priority Client\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 32\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500\",\n            children: \"|\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600\",\n            children: team.shift\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: team.logo ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: team.logo,\n          alt: `${team.name} logo`,\n          className: \"w-12 h-12 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-lg font-medium text-gray-500\",\n          children: ((_team$name = team.name) === null || _team$name === void 0 ? void 0 : (_team$name$ = _team$name[0]) === null || _team$name$ === void 0 ? void 0 : _team$name$.toUpperCase()) || \"T\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined text-gray-400\",\n          children: \"person\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Lead:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-gray-900 dark:text-gray-100\",\n          children: team.teamLead\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined text-gray-400\",\n          children: \"payments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Status:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-gray-900 dark:text-gray-100\",\n          children: team.billingStatus\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 grid grid-cols-2 gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600 dark:text-gray-300\",\n          children: \"Total Members\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDC65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold\",\n            children: team.totalMembers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600 dark:text-gray-300\",\n          children: \"Billable Hours\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u23F1\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold\",\n            children: team.billableHours\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 5\n    }, this), team.billingRate > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-3 text-xs text-right text-gray-500\",\n      children: [\"Rate: $\", team.billingRate, \"/hr\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 3\n  }, this);\n};\n\n// Infinite Slider Component\n_c = TeamCard;\nconst InfiniteSlider = ({\n  teams\n}) => {\n  _s();\n  const sliderRef = useRef(null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [startX, setStartX] = useState(0);\n  const [scrollLeft, setScrollLeft] = useState(0);\n  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);\n\n  // Create infinite loop by duplicating teams\n  const infiniteTeams = [...teams, ...teams, ...teams];\n\n  // Auto-scroll functionality\n  useEffect(() => {\n    if (!autoScrollEnabled || isDragging || teams.length === 0) return;\n    const interval = setInterval(() => {\n      if (sliderRef.current) {\n        const slider = sliderRef.current;\n        const cardWidth = 320; // Approximate card width + gap\n        const maxScroll = cardWidth * teams.length;\n        if (slider.scrollLeft >= maxScroll) {\n          slider.scrollLeft = 0;\n        } else {\n          slider.scrollLeft += 1;\n        }\n      }\n    }, 30);\n    return () => clearInterval(interval);\n  }, [autoScrollEnabled, isDragging, teams.length]);\n\n  // Mouse drag handlers\n  const handleMouseDown = e => {\n    setIsDragging(true);\n    setAutoScrollEnabled(false);\n    setStartX(e.pageX - sliderRef.current.offsetLeft);\n    setScrollLeft(sliderRef.current.scrollLeft);\n  };\n  const handleMouseMove = e => {\n    if (!isDragging) return;\n    e.preventDefault();\n    const x = e.pageX - sliderRef.current.offsetLeft;\n    const walk = (x - startX) * 2;\n    sliderRef.current.scrollLeft = scrollLeft - walk;\n  };\n  const handleMouseUp = () => {\n    setIsDragging(false);\n    setTimeout(() => setAutoScrollEnabled(true), 2000); // Resume auto-scroll after 2s\n  };\n\n  // Touch handlers for mobile\n  const handleTouchStart = e => {\n    setIsDragging(true);\n    setAutoScrollEnabled(false);\n    setStartX(e.touches[0].pageX - sliderRef.current.offsetLeft);\n    setScrollLeft(sliderRef.current.scrollLeft);\n  };\n  const handleTouchMove = e => {\n    if (!isDragging) return;\n    const x = e.touches[0].pageX - sliderRef.current.offsetLeft;\n    const walk = (x - startX) * 2;\n    sliderRef.current.scrollLeft = scrollLeft - walk;\n  };\n  const handleTouchEnd = () => {\n    setIsDragging(false);\n    setTimeout(() => setAutoScrollEnabled(true), 2000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: sliderRef,\n      className: \"flex gap-4 overflow-x-auto scrollbar-hide cursor-grab active:cursor-grabbing\",\n      style: {\n        scrollbarWidth: 'none',\n        msOverflowStyle: 'none',\n        WebkitOverflowScrolling: 'touch'\n      },\n      onMouseDown: handleMouseDown,\n      onMouseMove: handleMouseMove,\n      onMouseUp: handleMouseUp,\n      onMouseLeave: handleMouseUp,\n      onTouchStart: handleTouchStart,\n      onTouchMove: handleTouchMove,\n      onTouchEnd: handleTouchEnd,\n      children: infiniteTeams.map((team, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-shrink-0 w-80\",\n        children: /*#__PURE__*/_jsxDEV(TeamCard, {\n          team: team\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this)\n      }, `${team.id}-${index}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-100 to-transparent pointer-events-none\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-100 to-transparent pointer-events-none\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n\n// Main component\n_s(InfiniteSlider, \"iCaoEGp6Dctr2xB2kfOrXVBkGeo=\");\n_c2 = InfiniteSlider;\nfunction ClientTeamsSection() {\n  _s2();\n  const [teams, setTeams] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [err, setErr] = useState(null);\n  useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    if (!token) {\n      setErr(\"No auth token found\");\n      setLoading(false);\n      return;\n    }\n    const load = async () => {\n      setLoading(true);\n      try {\n        const mockTeams = [{\n          name: \"AccuWeather\",\n          team_lead: \"Kamal Hossain\",\n          total_members: 10,\n          billable_hours: 80,\n          logo: \"/assets/client-logos/accuweather.png\",\n          is_top_client: true\n        }, {\n          name: \"Bloomberg\",\n          team_lead: \"Kamal Hossain\",\n          total_members: 8,\n          billable_hours: 64,\n          logo: \"/assets/client-logos/bloomberg.png\",\n          is_top_client: true\n        }, {\n          name: \"Boats Group\",\n          team_lead: \"Aminul Islam\",\n          total_members: 13,\n          billable_hours: 104,\n          logo: \"/assets/client-logos/boats-group.png\",\n          is_top_client: true\n        }, {\n          name: \"Clipcentric\",\n          team_lead: \"Aminul Islam\",\n          total_members: 15,\n          billable_hours: 120,\n          logo: \"/assets/client-logos/clipcentric.png\"\n        }, {\n          name: \"MultiView\",\n          team_lead: \"Hasan Ahmed\",\n          total_members: 5,\n          billable_hours: 40,\n          logo: \"/assets/client-logos/multiview.png\"\n        }, {\n          name: \"Bigtincan\",\n          team_lead: \"Nafiul Islam\",\n          total_members: 5,\n          billable_hours: 40,\n          logo: \"/assets/client-logos/bigtincan.png\"\n        }];\n        let finalTeams = mockTeams;\n        try {\n          const response = await fetch(`${API_URL}/teams`, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json',\n              'Accept': 'application/json'\n            }\n          });\n          if (response.ok) {\n            var _data$data;\n            const data = await response.json();\n            if ((data === null || data === void 0 ? void 0 : (_data$data = data.data) === null || _data$data === void 0 ? void 0 : _data$data.length) > 0 || Array.isArray(data) && data.length > 0) {\n              finalTeams = (data === null || data === void 0 ? void 0 : data.data) || data;\n            }\n          }\n        } catch (error) {\n          console.warn('Failed to fetch from API, using mock data:', error);\n        }\n        setTeams(finalTeams.map((item, idx) => normalizeTeam(item, idx)));\n      } catch (error) {\n        console.error(\"Error fetching teams:\", error);\n        setErr(\"Unable to load teams. Please try again later.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    load();\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 273,\n    columnNumber: 23\n  }, this);\n  if (err) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-red-500\",\n    children: err\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 274,\n    columnNumber: 19\n  }, this);\n  if (!teams.length) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4\",\n    children: teams.map(team => /*#__PURE__*/_jsxDEV(TeamCard, {\n      team: team\n    }, team.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 278,\n    columnNumber: 5\n  }, this);\n}\n\n// Export the component\n_s2(ClientTeamsSection, \"91r8IFH4UttcDHrYvjpyXegUIh4=\");\n_c3 = ClientTeamsSection;\nexport default ClientTeamsSection;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"TeamCard\");\n$RefreshReg$(_c2, \"InfiniteSlider\");\n$RefreshReg$(_c3, \"ClientTeamsSection\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Loading", "useDashboardTeams", "jsxDEV", "_jsxDEV", "TeamCard", "team", "_team$name", "_team$name$", "className", "children", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isTopClient", "title", "shift", "logo", "src", "alt", "toUpperCase", "teamLead", "billingStatus", "totalMembers", "billableHours", "billingRate", "_c", "InfiniteSlider", "teams", "_s", "sliderRef", "isDragging", "setIsDragging", "startX", "setStartX", "scrollLeft", "setScrollLeft", "autoScrollEnabled", "setAutoScrollEnabled", "infiniteTeams", "length", "interval", "setInterval", "current", "slider", "<PERSON><PERSON><PERSON><PERSON>", "maxScroll", "clearInterval", "handleMouseDown", "e", "pageX", "offsetLeft", "handleMouseMove", "preventDefault", "x", "walk", "handleMouseUp", "setTimeout", "handleTouchStart", "touches", "handleTouchMove", "handleTouchEnd", "ref", "style", "scrollbarWidth", "msOverflowStyle", "WebkitOverflowScrolling", "onMouseDown", "onMouseMove", "onMouseUp", "onMouseLeave", "onTouchStart", "onTouchMove", "onTouchEnd", "map", "index", "id", "_c2", "ClientTeamsSection", "_s2", "setTeams", "loading", "setLoading", "err", "setErr", "token", "localStorage", "getItem", "load", "mockTeams", "team_lead", "total_members", "billable_hours", "is_top_client", "finalTeams", "response", "fetch", "API_URL", "headers", "ok", "_data$data", "data", "json", "Array", "isArray", "error", "console", "warn", "item", "idx", "normalizeTeam", "_c3", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/dashboard/ClientTeamsSection.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport Loading from \"../common/Loading\";\r\nimport { useDashboardTeams } from \"../hooks/useTeamData\";\r\n\r\n// Component to display a single team card\r\nconst TeamCard = ({ team }) => (\r\n  <div className=\"rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow p-4\">\r\n    <div className=\"flex items-center justify-between\">\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 truncate\">{team.name}</h3>\r\n        <div className=\"flex items-center gap-2 mt-1\">\r\n          {team.isTopClient && <span title=\"Top Client\" className=\"text-amber-500 text-sm\">🔆 Priority Client</span>}\r\n          <span className=\"text-xs text-gray-500\">|</span>\r\n          <span className=\"text-sm text-gray-600\">{team.shift}</span>\r\n        </div>\r\n      </div>\r\n      <div className=\"flex items-center gap-2\">\r\n        {team.logo ? (\r\n          <img\r\n            src={team.logo}\r\n            alt={`${team.name} logo`}\r\n            className=\"w-12 h-12 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-600\"\r\n          />\r\n        ) : (\r\n          <div className=\"w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-lg font-medium text-gray-500\">\r\n            {team.name?.[0]?.toUpperCase() || \"T\"}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n\r\n    <div className=\"mt-4 space-y-2\">\r\n      <p className=\"text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2\">\r\n        <span className=\"material-symbols-outlined text-gray-400\">person</span>\r\n        <span>Lead:</span>\r\n        <span className=\"font-medium text-gray-900 dark:text-gray-100\">{team.teamLead}</span>\r\n      </p>\r\n      \r\n      <p className=\"text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2\">\r\n        <span className=\"material-symbols-outlined text-gray-400\">payments</span>\r\n        <span>Status:</span>\r\n        <span className=\"font-medium text-gray-900 dark:text-gray-100\">{team.billingStatus}</span>\r\n      </p>\r\n    </div>\r\n\r\n    <div className=\"mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 grid grid-cols-2 gap-3\">\r\n      <div className=\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\">\r\n        <p className=\"text-xs text-gray-600 dark:text-gray-300\">Total Members</p>\r\n        <div className=\"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\">\r\n          <span>👥</span>\r\n          <span className=\"font-semibold\">{team.totalMembers}</span>\r\n        </div>\r\n      </div>\r\n      <div className=\"rounded-xl border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/40 p-3\">\r\n        <p className=\"text-xs text-gray-600 dark:text-gray-300\">Billable Hours</p>\r\n        <div className=\"mt-1 flex items-center gap-2 text-gray-900 dark:text-gray-100\">\r\n          <span>⏱️</span>\r\n          <span className=\"font-semibold\">{team.billableHours}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    {team.billingRate > 0 && (\r\n      <div className=\"mt-3 text-xs text-right text-gray-500\">\r\n        Rate: ${team.billingRate}/hr\r\n      </div>\r\n    )}\r\n  </div>\r\n);\r\n\r\n// Infinite Slider Component\r\nconst InfiniteSlider = ({ teams }) => {\r\n  const sliderRef = useRef(null);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [startX, setStartX] = useState(0);\r\n  const [scrollLeft, setScrollLeft] = useState(0);\r\n  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);\r\n\r\n  // Create infinite loop by duplicating teams\r\n  const infiniteTeams = [...teams, ...teams, ...teams];\r\n\r\n  // Auto-scroll functionality\r\n  useEffect(() => {\r\n    if (!autoScrollEnabled || isDragging || teams.length === 0) return;\r\n\r\n    const interval = setInterval(() => {\r\n      if (sliderRef.current) {\r\n        const slider = sliderRef.current;\r\n        const cardWidth = 320; // Approximate card width + gap\r\n        const maxScroll = cardWidth * teams.length;\r\n\r\n        if (slider.scrollLeft >= maxScroll) {\r\n          slider.scrollLeft = 0;\r\n        } else {\r\n          slider.scrollLeft += 1;\r\n        }\r\n      }\r\n    }, 30);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [autoScrollEnabled, isDragging, teams.length]);\r\n\r\n  // Mouse drag handlers\r\n  const handleMouseDown = (e) => {\r\n    setIsDragging(true);\r\n    setAutoScrollEnabled(false);\r\n    setStartX(e.pageX - sliderRef.current.offsetLeft);\r\n    setScrollLeft(sliderRef.current.scrollLeft);\r\n  };\r\n\r\n  const handleMouseMove = (e) => {\r\n    if (!isDragging) return;\r\n    e.preventDefault();\r\n    const x = e.pageX - sliderRef.current.offsetLeft;\r\n    const walk = (x - startX) * 2;\r\n    sliderRef.current.scrollLeft = scrollLeft - walk;\r\n  };\r\n\r\n  const handleMouseUp = () => {\r\n    setIsDragging(false);\r\n    setTimeout(() => setAutoScrollEnabled(true), 2000); // Resume auto-scroll after 2s\r\n  };\r\n\r\n  // Touch handlers for mobile\r\n  const handleTouchStart = (e) => {\r\n    setIsDragging(true);\r\n    setAutoScrollEnabled(false);\r\n    setStartX(e.touches[0].pageX - sliderRef.current.offsetLeft);\r\n    setScrollLeft(sliderRef.current.scrollLeft);\r\n  };\r\n\r\n  const handleTouchMove = (e) => {\r\n    if (!isDragging) return;\r\n    const x = e.touches[0].pageX - sliderRef.current.offsetLeft;\r\n    const walk = (x - startX) * 2;\r\n    sliderRef.current.scrollLeft = scrollLeft - walk;\r\n  };\r\n\r\n  const handleTouchEnd = () => {\r\n    setIsDragging(false);\r\n    setTimeout(() => setAutoScrollEnabled(true), 2000);\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative overflow-hidden\">\r\n      <div\r\n        ref={sliderRef}\r\n        className=\"flex gap-4 overflow-x-auto scrollbar-hide cursor-grab active:cursor-grabbing\"\r\n        style={{\r\n          scrollbarWidth: 'none',\r\n          msOverflowStyle: 'none',\r\n          WebkitOverflowScrolling: 'touch'\r\n        }}\r\n        onMouseDown={handleMouseDown}\r\n        onMouseMove={handleMouseMove}\r\n        onMouseUp={handleMouseUp}\r\n        onMouseLeave={handleMouseUp}\r\n        onTouchStart={handleTouchStart}\r\n        onTouchMove={handleTouchMove}\r\n        onTouchEnd={handleTouchEnd}\r\n      >\r\n        {infiniteTeams.map((team, index) => (\r\n          <div key={`${team.id}-${index}`} className=\"flex-shrink-0 w-80\">\r\n            <TeamCard team={team} />\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Gradient overlays for smooth edges */}\r\n      <div className=\"absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-100 to-transparent pointer-events-none\" />\r\n      <div className=\"absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-100 to-transparent pointer-events-none\" />\r\n    </div>\r\n  );\r\n};\r\n\r\n// Main component\r\nfunction ClientTeamsSection() {\r\n  const [teams, setTeams] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [err, setErr] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (!token) {\r\n      setErr(\"No auth token found\");\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    const load = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const mockTeams = [\r\n          {\r\n            name: \"AccuWeather\",\r\n            team_lead: \"Kamal Hossain\",\r\n            total_members: 10,\r\n            billable_hours: 80,\r\n            logo: \"/assets/client-logos/accuweather.png\",\r\n            is_top_client: true\r\n          },\r\n          {\r\n            name: \"Bloomberg\",\r\n            team_lead: \"Kamal Hossain\",\r\n            total_members: 8,\r\n            billable_hours: 64,\r\n            logo: \"/assets/client-logos/bloomberg.png\",\r\n            is_top_client: true\r\n          },\r\n          {\r\n            name: \"Boats Group\",\r\n            team_lead: \"Aminul Islam\",\r\n            total_members: 13,\r\n            billable_hours: 104,\r\n            logo: \"/assets/client-logos/boats-group.png\",\r\n            is_top_client: true\r\n          },\r\n          {\r\n            name: \"Clipcentric\",\r\n            team_lead: \"Aminul Islam\",\r\n            total_members: 15,\r\n            billable_hours: 120,\r\n            logo: \"/assets/client-logos/clipcentric.png\"\r\n          },\r\n          {\r\n            name: \"MultiView\",\r\n            team_lead: \"Hasan Ahmed\",\r\n            total_members: 5,\r\n            billable_hours: 40,\r\n            logo: \"/assets/client-logos/multiview.png\"\r\n          },\r\n          {\r\n            name: \"Bigtincan\",\r\n            team_lead: \"Nafiul Islam\",\r\n            total_members: 5,\r\n            billable_hours: 40,\r\n            logo: \"/assets/client-logos/bigtincan.png\"\r\n          }\r\n        ];\r\n\r\n        let finalTeams = mockTeams;\r\n\r\n        try {\r\n          const response = await fetch(`${API_URL}/teams`, {\r\n            headers: { \r\n              'Authorization': `Bearer ${token}`,\r\n              'Content-Type': 'application/json',\r\n              'Accept': 'application/json'\r\n            },\r\n          });\r\n\r\n          if (response.ok) {\r\n            const data = await response.json();\r\n            if (data?.data?.length > 0 || (Array.isArray(data) && data.length > 0)) {\r\n              finalTeams = data?.data || data;\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.warn('Failed to fetch from API, using mock data:', error);\r\n        }\r\n\r\n        setTeams(finalTeams.map((item, idx) => normalizeTeam(item, idx)));\r\n      } catch (error) {\r\n        console.error(\"Error fetching teams:\", error);\r\n        setErr(\"Unable to load teams. Please try again later.\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    load();\r\n  }, []);\r\n\r\n  if (loading) return <Loading />;\r\n  if (err) return <div className=\"text-red-500\">{err}</div>;\r\n  if (!teams.length) return null;\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4\">\r\n      {teams.map((team) => (\r\n        <TeamCard key={team.id} team={team} />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\n// Export the component\r\nexport default ClientTeamsSection;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,OAAO,MAAM,mBAAmB;AACvC,SAASC,iBAAiB,QAAQ,sBAAsB;;AAExD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC;EAAA,IAAAC,UAAA,EAAAC,WAAA;EAAA,oBACxBJ,OAAA;IAAKK,SAAS,EAAC,mIAAmI;IAAAC,QAAA,gBAChJN,OAAA;MAAKK,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDN,OAAA;QAAAM,QAAA,gBACEN,OAAA;UAAIK,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAAEJ,IAAI,CAACK;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChGX,OAAA;UAAKK,SAAS,EAAC,8BAA8B;UAAAC,QAAA,GAC1CJ,IAAI,CAACU,WAAW,iBAAIZ,OAAA;YAAMa,KAAK,EAAC,YAAY;YAACR,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1GX,OAAA;YAAMK,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDX,OAAA;YAAMK,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAEJ,IAAI,CAACY;UAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNX,OAAA;QAAKK,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EACrCJ,IAAI,CAACa,IAAI,gBACRf,OAAA;UACEgB,GAAG,EAAEd,IAAI,CAACa,IAAK;UACfE,GAAG,EAAE,GAAGf,IAAI,CAACK,IAAI,OAAQ;UACzBF,SAAS,EAAC;QAA6E;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,gBAEFX,OAAA;UAAKK,SAAS,EAAC,wHAAwH;UAAAC,QAAA,EACpI,EAAAH,UAAA,GAAAD,IAAI,CAACK,IAAI,cAAAJ,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAgBc,WAAW,CAAC,CAAC,KAAI;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENX,OAAA;MAAKK,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BN,OAAA;QAAGK,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAC7EN,OAAA;UAAMK,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvEX,OAAA;UAAAM,QAAA,EAAM;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBX,OAAA;UAAMK,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAEJ,IAAI,CAACiB;QAAQ;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC,eAEJX,OAAA;QAAGK,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAC7EN,OAAA;UAAMK,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzEX,OAAA;UAAAM,QAAA,EAAM;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpBX,OAAA;UAAMK,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAEJ,IAAI,CAACkB;QAAa;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENX,OAAA;MAAKK,SAAS,EAAC,gFAAgF;MAAAC,QAAA,gBAC7FN,OAAA;QAAKK,SAAS,EAAC,2FAA2F;QAAAC,QAAA,gBACxGN,OAAA;UAAGK,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzEX,OAAA;UAAKK,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAC5EN,OAAA;YAAAM,QAAA,EAAM;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfX,OAAA;YAAMK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEJ,IAAI,CAACmB;UAAY;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNX,OAAA;QAAKK,SAAS,EAAC,2FAA2F;QAAAC,QAAA,gBACxGN,OAAA;UAAGK,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC1EX,OAAA;UAAKK,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAC5EN,OAAA;YAAAM,QAAA,EAAM;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfX,OAAA;YAAMK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEJ,IAAI,CAACoB;UAAa;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELT,IAAI,CAACqB,WAAW,GAAG,CAAC,iBACnBvB,OAAA;MAAKK,SAAS,EAAC,uCAAuC;MAAAC,QAAA,GAAC,SAC9C,EAACJ,IAAI,CAACqB,WAAW,EAAC,KAC3B;IAAA;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAAA,CACP;;AAED;AAAAa,EAAA,GAjEMvB,QAAQ;AAkEd,MAAMwB,cAAc,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAMC,SAAS,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoC,MAAM,EAAEC,SAAS,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACwC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAM0C,aAAa,GAAG,CAAC,GAAGX,KAAK,EAAE,GAAGA,KAAK,EAAE,GAAGA,KAAK,CAAC;;EAEpD;EACAhC,SAAS,CAAC,MAAM;IACd,IAAI,CAACyC,iBAAiB,IAAIN,UAAU,IAAIH,KAAK,CAACY,MAAM,KAAK,CAAC,EAAE;IAE5D,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,IAAIZ,SAAS,CAACa,OAAO,EAAE;QACrB,MAAMC,MAAM,GAAGd,SAAS,CAACa,OAAO;QAChC,MAAME,SAAS,GAAG,GAAG,CAAC,CAAC;QACvB,MAAMC,SAAS,GAAGD,SAAS,GAAGjB,KAAK,CAACY,MAAM;QAE1C,IAAII,MAAM,CAACT,UAAU,IAAIW,SAAS,EAAE;UAClCF,MAAM,CAACT,UAAU,GAAG,CAAC;QACvB,CAAC,MAAM;UACLS,MAAM,CAACT,UAAU,IAAI,CAAC;QACxB;MACF;IACF,CAAC,EAAE,EAAE,CAAC;IAEN,OAAO,MAAMY,aAAa,CAACN,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACJ,iBAAiB,EAAEN,UAAU,EAAEH,KAAK,CAACY,MAAM,CAAC,CAAC;;EAEjD;EACA,MAAMQ,eAAe,GAAIC,CAAC,IAAK;IAC7BjB,aAAa,CAAC,IAAI,CAAC;IACnBM,oBAAoB,CAAC,KAAK,CAAC;IAC3BJ,SAAS,CAACe,CAAC,CAACC,KAAK,GAAGpB,SAAS,CAACa,OAAO,CAACQ,UAAU,CAAC;IACjDf,aAAa,CAACN,SAAS,CAACa,OAAO,CAACR,UAAU,CAAC;EAC7C,CAAC;EAED,MAAMiB,eAAe,GAAIH,CAAC,IAAK;IAC7B,IAAI,CAAClB,UAAU,EAAE;IACjBkB,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,MAAMC,CAAC,GAAGL,CAAC,CAACC,KAAK,GAAGpB,SAAS,CAACa,OAAO,CAACQ,UAAU;IAChD,MAAMI,IAAI,GAAG,CAACD,CAAC,GAAGrB,MAAM,IAAI,CAAC;IAC7BH,SAAS,CAACa,OAAO,CAACR,UAAU,GAAGA,UAAU,GAAGoB,IAAI;EAClD,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BxB,aAAa,CAAC,KAAK,CAAC;IACpByB,UAAU,CAAC,MAAMnB,oBAAoB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACtD,CAAC;;EAED;EACA,MAAMoB,gBAAgB,GAAIT,CAAC,IAAK;IAC9BjB,aAAa,CAAC,IAAI,CAAC;IACnBM,oBAAoB,CAAC,KAAK,CAAC;IAC3BJ,SAAS,CAACe,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,CAACT,KAAK,GAAGpB,SAAS,CAACa,OAAO,CAACQ,UAAU,CAAC;IAC5Df,aAAa,CAACN,SAAS,CAACa,OAAO,CAACR,UAAU,CAAC;EAC7C,CAAC;EAED,MAAMyB,eAAe,GAAIX,CAAC,IAAK;IAC7B,IAAI,CAAClB,UAAU,EAAE;IACjB,MAAMuB,CAAC,GAAGL,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,CAACT,KAAK,GAAGpB,SAAS,CAACa,OAAO,CAACQ,UAAU;IAC3D,MAAMI,IAAI,GAAG,CAACD,CAAC,GAAGrB,MAAM,IAAI,CAAC;IAC7BH,SAAS,CAACa,OAAO,CAACR,UAAU,GAAGA,UAAU,GAAGoB,IAAI;EAClD,CAAC;EAED,MAAMM,cAAc,GAAGA,CAAA,KAAM;IAC3B7B,aAAa,CAAC,KAAK,CAAC;IACpByB,UAAU,CAAC,MAAMnB,oBAAoB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EACpD,CAAC;EAED,oBACEpC,OAAA;IAAKK,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCN,OAAA;MACE4D,GAAG,EAAEhC,SAAU;MACfvB,SAAS,EAAC,8EAA8E;MACxFwD,KAAK,EAAE;QACLC,cAAc,EAAE,MAAM;QACtBC,eAAe,EAAE,MAAM;QACvBC,uBAAuB,EAAE;MAC3B,CAAE;MACFC,WAAW,EAAEnB,eAAgB;MAC7BoB,WAAW,EAAEhB,eAAgB;MAC7BiB,SAAS,EAAEb,aAAc;MACzBc,YAAY,EAAEd,aAAc;MAC5Be,YAAY,EAAEb,gBAAiB;MAC/Bc,WAAW,EAAEZ,eAAgB;MAC7Ba,UAAU,EAAEZ,cAAe;MAAArD,QAAA,EAE1B+B,aAAa,CAACmC,GAAG,CAAC,CAACtE,IAAI,EAAEuE,KAAK,kBAC7BzE,OAAA;QAAiCK,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eAC7DN,OAAA,CAACC,QAAQ;UAACC,IAAI,EAAEA;QAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GADhB,GAAGT,IAAI,CAACwE,EAAE,IAAID,KAAK,EAAE;QAAAjE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE1B,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNX,OAAA;MAAKK,SAAS,EAAC;IAAsG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxHX,OAAA;MAAKK,SAAS,EAAC;IAAuG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtH,CAAC;AAEV,CAAC;;AAED;AAAAgB,EAAA,CAxGMF,cAAc;AAAAkD,GAAA,GAAdlD,cAAc;AAyGpB,SAASmD,kBAAkBA,CAAA,EAAG;EAAAC,GAAA;EAC5B,MAAM,CAACnD,KAAK,EAAEoD,QAAQ,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoF,OAAO,EAAEC,UAAU,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsF,GAAG,EAAEC,MAAM,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EAEpCD,SAAS,CAAC,MAAM;IACd,MAAMyF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVD,MAAM,CAAC,qBAAqB,CAAC;MAC7BF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,MAAMM,IAAI,GAAG,MAAAA,CAAA,KAAY;MACvBN,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMO,SAAS,GAAG,CAChB;UACEhF,IAAI,EAAE,aAAa;UACnBiF,SAAS,EAAE,eAAe;UAC1BC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,EAAE;UAClB3E,IAAI,EAAE,sCAAsC;UAC5C4E,aAAa,EAAE;QACjB,CAAC,EACD;UACEpF,IAAI,EAAE,WAAW;UACjBiF,SAAS,EAAE,eAAe;UAC1BC,aAAa,EAAE,CAAC;UAChBC,cAAc,EAAE,EAAE;UAClB3E,IAAI,EAAE,oCAAoC;UAC1C4E,aAAa,EAAE;QACjB,CAAC,EACD;UACEpF,IAAI,EAAE,aAAa;UACnBiF,SAAS,EAAE,cAAc;UACzBC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,GAAG;UACnB3E,IAAI,EAAE,sCAAsC;UAC5C4E,aAAa,EAAE;QACjB,CAAC,EACD;UACEpF,IAAI,EAAE,aAAa;UACnBiF,SAAS,EAAE,cAAc;UACzBC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,GAAG;UACnB3E,IAAI,EAAE;QACR,CAAC,EACD;UACER,IAAI,EAAE,WAAW;UACjBiF,SAAS,EAAE,aAAa;UACxBC,aAAa,EAAE,CAAC;UAChBC,cAAc,EAAE,EAAE;UAClB3E,IAAI,EAAE;QACR,CAAC,EACD;UACER,IAAI,EAAE,WAAW;UACjBiF,SAAS,EAAE,cAAc;UACzBC,aAAa,EAAE,CAAC;UAChBC,cAAc,EAAE,EAAE;UAClB3E,IAAI,EAAE;QACR,CAAC,CACF;QAED,IAAI6E,UAAU,GAAGL,SAAS;QAE1B,IAAI;UACF,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,QAAQ,EAAE;YAC/CC,OAAO,EAAE;cACP,eAAe,EAAE,UAAUb,KAAK,EAAE;cAClC,cAAc,EAAE,kBAAkB;cAClC,QAAQ,EAAE;YACZ;UACF,CAAC,CAAC;UAEF,IAAIU,QAAQ,CAACI,EAAE,EAAE;YAAA,IAAAC,UAAA;YACf,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;YAClC,IAAI,CAAAD,IAAI,aAAJA,IAAI,wBAAAD,UAAA,GAAJC,IAAI,CAAEA,IAAI,cAAAD,UAAA,uBAAVA,UAAA,CAAY5D,MAAM,IAAG,CAAC,IAAK+D,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,IAAIA,IAAI,CAAC7D,MAAM,GAAG,CAAE,EAAE;cACtEsD,UAAU,GAAG,CAAAO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEA,IAAI,KAAIA,IAAI;YACjC;UACF;QACF,CAAC,CAAC,OAAOI,KAAK,EAAE;UACdC,OAAO,CAACC,IAAI,CAAC,4CAA4C,EAAEF,KAAK,CAAC;QACnE;QAEAzB,QAAQ,CAACc,UAAU,CAACpB,GAAG,CAAC,CAACkC,IAAI,EAAEC,GAAG,KAAKC,aAAa,CAACF,IAAI,EAAEC,GAAG,CAAC,CAAC,CAAC;MACnE,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CrB,MAAM,CAAC,+CAA+C,CAAC;MACzD,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDM,IAAI,CAAC,CAAC;EACR,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIP,OAAO,EAAE,oBAAO/E,OAAA,CAACH,OAAO;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC/B,IAAIsE,GAAG,EAAE,oBAAOjF,OAAA;IAAKK,SAAS,EAAC,cAAc;IAAAC,QAAA,EAAE2E;EAAG;IAAAzE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACzD,IAAI,CAACe,KAAK,CAACY,MAAM,EAAE,OAAO,IAAI;EAE9B,oBACEtC,OAAA;IAAKK,SAAS,EAAC,qFAAqF;IAAAC,QAAA,EACjGoB,KAAK,CAAC8C,GAAG,CAAEtE,IAAI,iBACdF,OAAA,CAACC,QAAQ;MAAeC,IAAI,EAAEA;IAAK,GAApBA,IAAI,CAACwE,EAAE;MAAAlE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAe,CACtC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;;AAEA;AAAAkE,GAAA,CA7GSD,kBAAkB;AAAAiC,GAAA,GAAlBjC,kBAAkB;AA8G3B,eAAeA,kBAAkB;AAAC,IAAApD,EAAA,EAAAmD,GAAA,EAAAkC,GAAA;AAAAC,YAAA,CAAAtF,EAAA;AAAAsF,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}