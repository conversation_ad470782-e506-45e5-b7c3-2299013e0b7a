<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('password_managers', function (Blueprint $table) {
            $table->id();

            // Optional reference fields, without foreign key constraints
            $table->unsignedBigInteger('department_id')->nullable(); // Just to store selected department
            $table->unsignedBigInteger('team_id')->nullable();       // Just to store selected team
            $table->unsignedBigInteger('user_id');                   // Creator or Owner of the password

            // Main data fields
            $table->string('password_title'); // Title of the password (e.g., Docker)
            $table->string('username');       // Username for the service
            $table->longText('password');     // Encrypted password

            // Timestamps
            $table->timestamps();

            // Indexes for better performance
            $table->index(['user_id']);
            $table->index(['department_id', 'team_id']);
            $table->index(['user_id', 'password_title']);
            $table->index('created_at');

           
        });
    }

    public function down()
    {
        Schema::dropIfExists('password_managers');
    }
};